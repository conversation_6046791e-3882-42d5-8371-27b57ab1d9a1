// BaseFormWithParameters - Base form class with automatic parameter injection
// Usage: Inherit from this class to automatically apply parameter-based formatting and values

using System;
using System.Diagnostics;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using ProManage.Modules.Services;

namespace ProManage.Modules.UI
{
    /// <summary>
    /// Base form class that provides automatic parameter injection and formatting
    /// Forms inheriting from this class will automatically have parameter-based values applied
    /// </summary>
    public partial class BaseFormWithParameters : XtraForm
    {
        #region Properties

        /// <summary>
        /// Gets or sets whether parameter bindings should be applied automatically on load
        /// </summary>
        protected bool AutoApplyParameterBindings { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to refresh parameters before applying bindings
        /// </summary>
        protected bool RefreshParametersOnLoad { get; set; } = false;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the BaseFormWithParameters class
        /// </summary>
        public BaseFormWithParameters()
        {
            InitializeComponent();
        }

        #endregion

        #region Overridden Methods

        /// <summary>
        /// Raises the Load event and applies parameter bindings
        /// </summary>
        /// <param name="e">Event arguments</param>
        protected override void OnLoad(EventArgs e)
        {
            try
            {
                base.OnLoad(e);

                if (AutoApplyParameterBindings && !DesignMode)
                {
                    ApplyParameterBindings();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in BaseFormWithParameters.OnLoad: {ex.Message}");
                // Don't throw - form should still load even if parameter binding fails
            }
        }

        #endregion

        #region Parameter Binding Methods

        /// <summary>
        /// Applies parameter-based bindings to form controls
        /// Override this method in derived classes for custom parameter binding logic
        /// </summary>
        protected virtual void ApplyParameterBindings()
        {
            try
            {
                Debug.WriteLine($"=== BaseFormWithParameters.ApplyParameterBindings: Starting for {this.GetType().Name} ===");

                // Refresh parameters if requested
                if (RefreshParametersOnLoad)
                {
                    TypedParameterManager.RefreshParameters();
                }

                // Check if parameter cache is ready
                if (!TypedParameterManager.IsParameterCacheReady())
                {
                    Debug.WriteLine("Parameter cache not ready, skipping parameter bindings");
                    return;
                }

                // Apply parameter bindings to controls
                ApplyParameterBindingsToControls(this);

                Debug.WriteLine($"=== BaseFormWithParameters.ApplyParameterBindings: Completed for {this.GetType().Name} ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying parameter bindings: {ex.Message}");
            }
        }

        /// <summary>
        /// Recursively applies parameter bindings to controls based on their Tag property
        /// </summary>
        /// <param name="container">The container control to process</param>
        protected virtual void ApplyParameterBindingsToControls(Control container)
        {
            try
            {
                foreach (Control control in container.Controls)
                {
                    // Process the current control
                    ApplyParameterBindingToControl(control);

                    // Recursively process child controls
                    if (control.HasChildren)
                    {
                        ApplyParameterBindingsToControls(control);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying parameter bindings to controls: {ex.Message}");
            }
        }

        /// <summary>
        /// Applies parameter binding to a specific control based on its Tag property
        /// </summary>
        /// <param name="control">The control to process</param>
        protected virtual void ApplyParameterBindingToControl(Control control)
        {
            try
            {
                if (control.Tag == null) return;

                string tag = control.Tag.ToString();
                if (string.IsNullOrWhiteSpace(tag)) return;

                Debug.WriteLine($"Processing control with tag: {tag}");

                // Apply parameter bindings based on tag value
                switch (tag.ToUpperInvariant())
                {
                    case "CURRENCY":
                        ApplyCurrencyBinding(control);
                        break;

                    case "COMPANY_NAME":
                        ApplyCompanyNameBinding(control);
                        break;

                    case "COMPANY_ADDRESS":
                        ApplyCompanyAddressBinding(control);
                        break;

                    case "DATE_FORMAT":
                        ApplyDateFormatBinding(control);
                        break;

                    case "GST":
                        ApplyGSTVisibilityBinding(control);
                        break;

                    case "APPLY_DECIMALS":
                        ApplyDecimalFormatBinding(control);
                        break;

                    case "APPLY_CURRENCY_FORMAT":
                        ApplyCurrencyFormatBinding(control);
                        break;

                    default:
                        // Try to apply as a direct parameter lookup
                        ApplyDirectParameterBinding(control, tag);
                        break;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying parameter binding to control {control.Name}: {ex.Message}");
            }
        }

        #endregion

        #region Specific Parameter Binding Methods

        /// <summary>
        /// Applies currency parameter binding to a control
        /// </summary>
        /// <param name="control">The control to bind</param>
        protected virtual void ApplyCurrencyBinding(Control control)
        {
            try
            {
                string currency = UnifiedParameterManager.Instance.Currency.Symbol;

                if (control is Label label)
                {
                    label.Text = currency;
                    Debug.WriteLine($"Applied currency '{currency}' to label {control.Name}");
                }
                else if (control is TextEdit textEdit)
                {
                    textEdit.Text = currency;
                    Debug.WriteLine($"Applied currency '{currency}' to text edit {control.Name}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying currency binding: {ex.Message}");
            }
        }

        /// <summary>
        /// Applies company name parameter binding to a control
        /// </summary>
        /// <param name="control">The control to bind</param>
        protected virtual void ApplyCompanyNameBinding(Control control)
        {
            try
            {
                string companyName = UnifiedParameterManager.Instance.Company.Name;

                if (control is Label label)
                {
                    label.Text = companyName;
                    Debug.WriteLine($"Applied company name '{companyName}' to label {control.Name}");
                }
                else if (control is TextEdit textEdit)
                {
                    textEdit.Text = companyName;
                    Debug.WriteLine($"Applied company name '{companyName}' to text edit {control.Name}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying company name binding: {ex.Message}");
            }
        }

        /// <summary>
        /// Applies company address parameter binding to a control
        /// </summary>
        /// <param name="control">The control to bind</param>
        protected virtual void ApplyCompanyAddressBinding(Control control)
        {
            try
            {
                string companyAddress = UnifiedParameterManager.Instance.Company.Address;

                if (control is Label label)
                {
                    label.Text = companyAddress;
                    Debug.WriteLine($"Applied company address to label {control.Name}");
                }
                else if (control is TextEdit textEdit)
                {
                    textEdit.Text = companyAddress;
                    Debug.WriteLine($"Applied company address to text edit {control.Name}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying company address binding: {ex.Message}");
            }
        }

        /// <summary>
        /// Applies GST visibility binding to a control
        /// </summary>
        /// <param name="control">The control to bind</param>
        protected virtual void ApplyGSTVisibilityBinding(Control control)
        {
            try
            {
                bool showGST = UnifiedParameterManager.Instance.Business.ShowGST;
                control.Visible = showGST;
                Debug.WriteLine($"Applied GST visibility '{showGST}' to control {control.Name}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying GST visibility binding: {ex.Message}");
            }
        }

        /// <summary>
        /// Applies decimal formatting to grid columns
        /// </summary>
        /// <param name="control">The control to bind (should be GridControl)</param>
        protected virtual void ApplyDecimalFormatBinding(Control control)
        {
            try
            {
                if (control is GridControl gridControl)
                {
                    var gridView = gridControl.MainView as GridView;
                    if (gridView != null)
                    {
                        int decimals = UnifiedParameterManager.Instance.Currency.DecimalPlaces;
                        string format = $"N{decimals}";

                        foreach (GridColumn column in gridView.Columns)
                        {
                            if (column.Tag?.ToString() == "ApplyDecimals")
                            {
                                column.DisplayFormat.FormatString = format;
                                column.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                                Debug.WriteLine($"Applied decimal format '{format}' to column {column.FieldName}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying decimal format binding: {ex.Message}");
            }
        }

        /// <summary>
        /// Applies currency formatting to grid columns
        /// </summary>
        /// <param name="control">The control to bind (should be GridControl)</param>
        protected virtual void ApplyCurrencyFormatBinding(Control control)
        {
            try
            {
                if (control is GridControl gridControl)
                {
                    var gridView = gridControl.MainView as GridView;
                    if (gridView != null)
                    {
                        string currencyFormat = UnifiedParameterManager.Instance.Currency.Format;

                        foreach (GridColumn column in gridView.Columns)
                        {
                            if (column.Tag?.ToString() == "ApplyCurrencyFormat")
                            {
                                column.DisplayFormat.FormatString = currencyFormat;
                                column.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                                Debug.WriteLine($"Applied currency format '{currencyFormat}' to column {column.FieldName}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying currency format binding: {ex.Message}");
            }
        }

        /// <summary>
        /// Applies date format binding to a control
        /// </summary>
        /// <param name="control">The control to bind</param>
        protected virtual void ApplyDateFormatBinding(Control control)
        {
            try
            {
                string dateFormat = UnifiedParameterManager.Instance.UI.DateFormat;
                
                if (control is DateEdit dateEdit)
                {
                    dateEdit.Properties.DisplayFormat.FormatString = dateFormat;
                    dateEdit.Properties.EditFormat.FormatString = dateFormat;
                    Debug.WriteLine($"Applied date format '{dateFormat}' to date edit {control.Name}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying date format binding: {ex.Message}");
            }
        }

        /// <summary>
        /// Applies direct parameter binding using the tag as parameter code
        /// </summary>
        /// <param name="control">The control to bind</param>
        /// <param name="parameterCode">The parameter code to lookup</param>
        protected virtual void ApplyDirectParameterBinding(Control control, string parameterCode)
        {
            try
            {
                string parameterValue = ParameterCacheService.Instance.GetParameter(parameterCode);
                
                if (!string.IsNullOrEmpty(parameterValue))
                {
                    if (control is Label label)
                    {
                        label.Text = parameterValue;
                        Debug.WriteLine($"Applied parameter '{parameterCode}' = '{parameterValue}' to label {control.Name}");
                    }
                    else if (control is TextEdit textEdit)
                    {
                        textEdit.Text = parameterValue;
                        Debug.WriteLine($"Applied parameter '{parameterCode}' = '{parameterValue}' to text edit {control.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying direct parameter binding for '{parameterCode}': {ex.Message}");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Manually refreshes parameter bindings for the form
        /// Call this method when parameters have been updated
        /// </summary>
        public virtual void RefreshParameterBindings()
        {
            try
            {
                Debug.WriteLine($"Manually refreshing parameter bindings for {this.GetType().Name}");
                TypedParameterManager.RefreshParameters();
                ApplyParameterBindings();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing parameter bindings: {ex.Message}");
            }
        }

        #endregion
    }
}
