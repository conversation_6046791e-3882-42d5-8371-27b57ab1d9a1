
## 🔧 OBJECTIVE

Create a reusable **parameter injection system** so that:

* Parameters like `CURRENCY`, `DECIMALS`, `COMPANY_NAME`, `SHOW_GST` are loaded at app startup.
* All forms and reports **automatically receive and apply** the parameters.
* You **never repeat logic** across forms.
* Designers simply tag controls (`Tag = "Currency"`, `Tag = "GST"`) and the system handles the rest.

---

## 📁 FILE STRUCTURE

| File                              | Role                                                                                   |
| --------------------------------- | -------------------------------------------------------------------------------------- |
| `ParameterManager.vb`             | Loads parameters once from DB. Exposes them via `GetValue()`, `GetInt()`, `GetBool()`. |
| `BaseFormWithParams.vb`           | Common base form inherited by all forms needing parameter auto-binding.                |
| `YourForm.vb` (e.g. `frmInvoice`) | Inherits `BaseFormWithParams` and uses `.Tag` on controls to bind values.              |
| `Parameters` (Database Table)     | Contains all key-value pairs (e.g., `CURRENCY=USD`, `SHOW_GST=True`).                  |

---

## ✅ IMPLEMENTATION PLAN

---

### STEP 1: `ParameterManager.vb` (Singleton Class)

```vbnet
Public Class ParameterManager
    Private Shared _parameters As Dictionary(Of String, String)

    Public Shared Sub LoadParameters()
        _parameters = New Dictionary(Of String, String)(StringComparer.OrdinalIgnoreCase)
        Dim dt As DataTable = GetDataTable("SELECT ParamCode, ParamValue FROM Parameters")
        For Each row As DataRow In dt.Rows
            _parameters(row("ParamCode").ToString()) = row("ParamValue").ToString()
        Next
    End Sub

    Public Shared Function GetValue(code As String, Optional defaultValue As String = "") As String
        If _parameters.ContainsKey(code) Then Return _parameters(code)
        Return defaultValue
    End Function

    Public Shared Function GetInt(code As String, Optional defaultValue As Integer = 0) As Integer
        Dim val = GetValue(code)
        Return If(Integer.TryParse(val, Nothing), Integer.Parse(val), defaultValue)
    End Function

    Public Shared Function GetBool(code As String, Optional defaultValue As Boolean = False) As Boolean
        Dim val = GetValue(code)
        Return If(Boolean.TryParse(val, Nothing), Boolean.Parse(val), defaultValue)
    End Function
End Class
```

---

### STEP 2: Modify `Program.vb` (App Entry Point)

```vbnet
Sub Main()
    ' Load all global parameters once
    ParameterManager.LoadParameters()

    ' Continue with login or main form
    Application.Run(New frmLogin())
End Sub
```

---

### STEP 3: Create `BaseFormWithParams.vb`

```vbnet
Public Class BaseFormWithParams
    Inherits DevExpress.XtraEditors.XtraForm

    Protected Overrides Sub OnLoad(e As EventArgs)
        MyBase.OnLoad(e)
        ApplyParameterBindings()
    End Sub

    Protected Overridable Sub ApplyParameterBindings()
        Dim currency = ParameterManager.GetValue("CURRENCY", "AED")
        Dim company = ParameterManager.GetValue("COMPANY_NAME", "My Company")
        Dim decimals = ParameterManager.GetInt("DECIMALS", 2)
        Dim showGST = ParameterManager.GetBool("SHOW_GST", False)

        ' Loop controls
        For Each ctrl As Control In Me.Controls
            ' Simple Label injection
            If TypeOf ctrl Is Label Then
                If ctrl.Tag = "Currency" Then ctrl.Text = currency
                If ctrl.Tag = "CompanyName" Then ctrl.Text = company
            End If

            ' GST GroupBox visibility
            If TypeOf ctrl Is GroupBox AndAlso ctrl.Tag = "GST" Then
                ctrl.Visible = showGST
            End If

            ' Grid column formatting
            If TypeOf ctrl Is DevExpress.XtraGrid.GridControl Then
                Dim grid = CType(ctrl, DevExpress.XtraGrid.GridControl)
                Dim view = CType(grid.MainView, DevExpress.XtraGrid.Views.Grid.GridView)

                For Each col In view.Columns
                    If col.Tag = "ApplyDecimals" Then
                        col.DisplayFormat.FormatString = "N" & decimals
                        col.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
                    End If
                Next
            End If
        Next
    End Sub
End Class
```

---

### STEP 4: Use It in Your Forms

#### `frmInvoice.vb` or `frmQuotation.vb`

```vbnet
Public Class frmInvoice
    Inherits BaseFormWithParams
```

---

### STEP 5: Tag Controls in the Designer

| Control                 | Tag Value       | What Happens                            |
| ----------------------- | --------------- | --------------------------------------- |
| `lblCurrency`           | `Currency`      | Gets value of `CURRENCY` from parameter |
| `lblCompanyName`        | `CompanyName`   | Sets label to company name              |
| `grpGST` (GroupBox)     | `GST`           | Shows/hides based on `SHOW_GST`         |
| `colPrice` (GridColumn) | `ApplyDecimals` | Formats with `DECIMALS` value           |

---

## 🧠 HOW IT WORKS (INTERNALLY)

```
App Starts → ParameterManager.LoadParameters()
    ↓
Any Form Inheriting BaseFormWithParams
    ↓
OnLoad → ApplyParameterBindings() called
    ↓
Each Control is checked by Tag
    ↓
Correct parameter applied → currency label, decimals, visibility, etc.
```

---

## 🧱 OPTIONAL EXTENSIONS

| Feature                                          | Idea                                            |
| ------------------------------------------------ | ----------------------------------------------- |
| `ReloadParameters()`                             | Allow admin UI to refresh values                |
| `Tag = "TaxRate"`                                | Add more tags for other business logic          |
| ReportViewer forms                               | Also inherit `BaseFormWithParams` or copy logic |
| Disable button if parameter is "DISABLE\_EXPORT" | Use `ctrl.Enabled = False`                      |

---

## ✅ DEVELOPER/AGENT TASK SUMMARY

* Create 1 new file: `BaseFormWithParams.vb`
* Modify: `Program.vb → Main()` to call `ParameterManager.LoadParameters()`
* In each form:

  * Inherit from `BaseFormWithParams`
  * Set `.Tag` in Designer
* No more repetitive parameter fetching code


