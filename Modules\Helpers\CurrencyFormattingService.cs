using System;
using System.Diagnostics;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.Data;
using DevExpress.Utils;

namespace ProManage.Modules.Helpers
{
    /// <summary>
    /// Service for applying currency formatting to DevExpress GridView columns and summaries
    /// Provides centralized currency formatting functionality for estimate grids
    /// </summary>
    public static class CurrencyFormattingService
    {
        /// <summary>
        /// Applies currency formatting to specified price columns in a GridView
        /// </summary>
        /// <param name="gridView">The GridView to format</param>
        /// <param name="oeColumnName">The OE Price column name</param>
        /// <param name="afmColumnName">The AFM Price column name</param>
        public static void ApplyCurrencyFormatting(GridView gridView, string oeColumnName, string afmColumnName)
        {
            try
            {
                Debug.WriteLine($"=== CurrencyFormattingService.ApplyCurrencyFormatting: Starting ===");
                Debug.WriteLine($"Formatting columns: {oeColumnName}, {afmColumnName}");

                if (gridView == null)
                {
                    Debug.WriteLine("GridView is null, cannot apply currency formatting");
                    return;
                }

                // Format OE Price column
                var oeColumn = gridView.Columns[oeColumnName];
                if (oeColumn != null)
                {
                    oeColumn.DisplayFormat.FormatType = FormatType.Numeric;
                    oeColumn.DisplayFormat.FormatString = "AED {0:N2}";
                    Debug.WriteLine($"✓ Applied currency formatting to {oeColumnName}");
                }
                else
                {
                    Debug.WriteLine($"⚠ Column {oeColumnName} not found");
                }

                // Format AFM Price column
                var afmColumn = gridView.Columns[afmColumnName];
                if (afmColumn != null)
                {
                    afmColumn.DisplayFormat.FormatType = FormatType.Numeric;
                    afmColumn.DisplayFormat.FormatString = "AED {0:N2}";
                    Debug.WriteLine($"✓ Applied currency formatting to {afmColumnName}");
                }
                else
                {
                    Debug.WriteLine($"⚠ Column {afmColumnName} not found");
                }

                Debug.WriteLine("=== CurrencyFormattingService.ApplyCurrencyFormatting: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ApplyCurrencyFormatting: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Applies currency summary formatting to specified columns in a GridView
        /// </summary>
        /// <param name="gridView">The GridView to format</param>
        /// <param name="columnNames">Array of column names to apply currency summaries</param>
        public static void ApplyCurrencySummaries(GridView gridView, string[] columnNames)
        {
            try
            {
                Debug.WriteLine($"=== CurrencyFormattingService.ApplyCurrencySummaries: Starting ===");
                Debug.WriteLine($"Applying summaries to columns: {string.Join(", ", columnNames)}");

                if (gridView == null)
                {
                    Debug.WriteLine("GridView is null, cannot apply currency summaries");
                    return;
                }

                if (columnNames == null || columnNames.Length == 0)
                {
                    Debug.WriteLine("No column names provided");
                    return;
                }

                foreach (string columnName in columnNames)
                {
                    var column = gridView.Columns[columnName];
                    if (column != null)
                    {
                        // Clear existing summaries for this column
                        column.Summary.Clear();

                        // Add currency sum summary
                        var summaryItem = new GridColumnSummaryItem(SummaryItemType.Sum, columnName, "Total: AED {0:N2}");
                        column.Summary.Add(summaryItem);

                        Debug.WriteLine($"✓ Applied currency summary to {columnName}");
                    }
                    else
                    {
                        Debug.WriteLine($"⚠ Column {columnName} not found");
                    }
                }

                Debug.WriteLine("=== CurrencyFormattingService.ApplyCurrencySummaries: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ApplyCurrencySummaries: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Applies both currency formatting and summaries to price columns
        /// </summary>
        /// <param name="gridView">The GridView to format</param>
        /// <param name="oeColumnName">The OE Price column name</param>
        /// <param name="afmColumnName">The AFM Price column name</param>
        public static void ApplyCompleteCurrencyFormatting(GridView gridView, string oeColumnName, string afmColumnName)
        {
            try
            {
                Debug.WriteLine($"=== CurrencyFormattingService.ApplyCompleteCurrencyFormatting: Starting ===");

                // Apply basic currency formatting
                ApplyCurrencyFormatting(gridView, oeColumnName, afmColumnName);

                // Apply currency summaries
                ApplyCurrencySummaries(gridView, new[] { oeColumnName, afmColumnName });

                Debug.WriteLine("=== CurrencyFormattingService.ApplyCompleteCurrencyFormatting: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ApplyCompleteCurrencyFormatting: {ex.Message}");
                throw;
            }
        }
    }
}
