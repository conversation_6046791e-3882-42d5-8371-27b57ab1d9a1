# ParameterCacheServiceExtensions Usage Examples

## Overview

The `ParameterCacheServiceExtensions` class provides typed extension methods for the `ParameterCacheService`, enabling type-safe parameter access with automatic conversion and validation. This document provides comprehensive usage examples and best practices.

## Available Extension Methods

### 1. Integer Parameters - `GetInt()`

```csharp
// Basic usage with default value
int decimals = ParameterCacheService.Instance.GetInt("DECIMALS", 2);

// Usage in form initialization
public void InitializeFormatting()
{
    int decimalPlaces = ParameterCacheService.Instance.GetInt("DECIMAL_PLACES", 2);
    gridView.Columns["Amount"].DisplayFormat.FormatString = $"N{decimalPlaces}";
}
```

### 2. Boolean Parameters - `GetBool()`

```csharp
// Basic usage
bool enableLogging = ParameterCacheService.Instance.GetBool("ENABLE_LOGGING", true);

// Supports various boolean representations: true/false, 1/0, yes/no, on/off
bool enableFeature = ParameterCacheService.Instance.GetBool("ENABLE_FEATURE_X", false);

// Usage in conditional logic
if (ParameterCacheService.Instance.GetBool("SHOW_ADVANCED_OPTIONS", false))
{
    advancedPanel.Visible = true;
}
```

### 3. Decimal Parameters - `GetDecimal()`

```csharp
// Basic usage for financial calculations
decimal taxRate = ParameterCacheService.Instance.GetDecimal("TAX_RATE", 5.0m);
decimal totalWithTax = subtotal * (1 + taxRate / 100);

// Usage in grid calculations
decimal defaultDiscount = ParameterCacheService.Instance.GetDecimal("DEFAULT_DISCOUNT", 0.0m);
```

### 4. Double Parameters - `GetDouble()`

```csharp
// Basic usage for mathematical operations
double multiplier = ParameterCacheService.Instance.GetDouble("CALCULATION_MULTIPLIER", 1.0);
double result = inputValue * multiplier;

// Usage in measurement conversions
double conversionFactor = ParameterCacheService.Instance.GetDouble("UNIT_CONVERSION", 1.0);
```

### 5. DateTime Parameters - `GetDateTime()`

```csharp
// Basic usage with default to today
DateTime cutoffDate = ParameterCacheService.Instance.GetDateTime("CUTOFF_DATE", DateTime.Today);

// Usage with specific default date
DateTime fiscalYearStart = ParameterCacheService.Instance.GetDateTime("FISCAL_YEAR_START", new DateTime(2024, 4, 1));

// Usage in date filtering
DateTime reportStartDate = ParameterCacheService.Instance.GetDateTime("REPORT_START_DATE", DateTime.Today.AddDays(-30));
```

### 6. Enum Parameters - `GetEnum<T>()`

```csharp
// Define an enum for application settings
public enum LogLevel
{
    None = 0,
    Error = 1,
    Warning = 2,
    Info = 3,
    Debug = 4
}

// Usage with enum
LogLevel currentLogLevel = ParameterCacheService.Instance.GetEnum("LOG_LEVEL", LogLevel.Info);

// Usage with built-in enums
DayOfWeek startOfWeek = ParameterCacheService.Instance.GetEnum("WEEK_START_DAY", DayOfWeek.Monday);
```

## Real-World Usage Examples

### Example 1: EstimateForm Initialization

```csharp
public partial class EstimateForm : XtraForm
{
    private void InitializeFormSettings()
    {
        // Get formatting parameters
        int decimals = ParameterCacheService.Instance.GetInt("DECIMALS", 2);
        string currency = ParameterCacheService.Instance.GetParameter("CURRENCY", "USD");
        
        // Apply to grid columns
        gridView.Columns["Rate"].DisplayFormat.FormatString = $"N{decimals}";
        gridView.Columns["Amount"].DisplayFormat.FormatString = $"N{decimals}";
        
        // Set currency label
        lblCurrency.Text = currency;
        
        // Get business rules
        bool allowNegativeAmounts = ParameterCacheService.Instance.GetBool("ALLOW_NEGATIVE_AMOUNTS", false);
        decimal maxDiscountPercent = ParameterCacheService.Instance.GetDecimal("MAX_DISCOUNT_PERCENT", 10.0m);
        
        // Apply business rules
        if (!allowNegativeAmounts)
        {
            // Add validation for positive amounts only
        }
    }
}
```

### Example 2: Report Generation

```csharp
public class ReportGenerator
{
    public void GenerateMonthlyReport()
    {
        // Get report parameters
        DateTime reportStartDate = ParameterCacheService.Instance.GetDateTime("REPORT_START_DATE", DateTime.Today.AddDays(-30));
        DateTime reportEndDate = ParameterCacheService.Instance.GetDateTime("REPORT_END_DATE", DateTime.Today);
        
        bool includeInactiveRecords = ParameterCacheService.Instance.GetBool("INCLUDE_INACTIVE_RECORDS", false);
        int maxRecordsPerPage = ParameterCacheService.Instance.GetInt("REPORT_PAGE_SIZE", 50);
        
        // Generate report with parameters
        var reportData = GetReportData(reportStartDate, reportEndDate, includeInactiveRecords, maxRecordsPerPage);
    }
}
```

### Example 3: Application Configuration

```csharp
public class ApplicationSettings
{
    public static void LoadSettings()
    {
        // Load UI settings
        bool showToolTips = ParameterCacheService.Instance.GetBool("SHOW_TOOLTIPS", true);
        int autoSaveInterval = ParameterCacheService.Instance.GetInt("AUTOSAVE_INTERVAL_MINUTES", 5);
        
        // Load business settings
        decimal defaultTaxRate = ParameterCacheService.Instance.GetDecimal("DEFAULT_TAX_RATE", 0.0m);
        DateTime fiscalYearEnd = ParameterCacheService.Instance.GetDateTime("FISCAL_YEAR_END", new DateTime(DateTime.Now.Year, 12, 31));
        
        // Apply settings
        Application.SetToolTipEnabled(showToolTips);
        Application.SetAutoSaveInterval(TimeSpan.FromMinutes(autoSaveInterval));
    }
}
```

## Error Handling and Debugging

### Enhanced Logging Method

```csharp
// Use GetParameterWithLogging for debugging
string debugValue = ParameterCacheService.Instance.GetParameterWithLogging("DEBUG_PARAMETER", "default");
```

### Best Practices

1. **Always provide meaningful default values**:
   ```csharp
   // Good: Meaningful default
   int pageSize = ParameterCacheService.Instance.GetInt("PAGE_SIZE", 25);
   
   // Avoid: Generic default that might not make sense
   int pageSize = ParameterCacheService.Instance.GetInt("PAGE_SIZE", 0);
   ```

2. **Use appropriate data types**:
   ```csharp
   // Use decimal for financial calculations
   decimal taxRate = ParameterCacheService.Instance.GetDecimal("TAX_RATE", 5.0m);
   
   // Use double for scientific calculations
   double conversionFactor = ParameterCacheService.Instance.GetDouble("CONVERSION_FACTOR", 1.0);
   ```

3. **Handle edge cases**:
   ```csharp
   // Validate ranges after getting parameter values
   int maxRetries = ParameterCacheService.Instance.GetInt("MAX_RETRIES", 3);
   if (maxRetries < 1) maxRetries = 1; // Ensure at least one retry
   if (maxRetries > 10) maxRetries = 10; // Cap at reasonable maximum
   ```

## Backward Compatibility

The new extension methods are fully backward compatible. Existing code using the original methods continues to work:

```csharp
// Original methods still work
string currency = ParameterCacheService.Instance.GetParameter("CURRENCY");
string decimals = ParameterCacheService.Instance.GetParameter("DECIMALS", "2");

// New typed methods provide additional functionality
int decimalsTyped = ParameterCacheService.Instance.GetInt("DECIMALS", 2);
```

## Performance Considerations

- Extension methods have minimal overhead
- Type conversion is performed only when needed
- Failed conversions fall back to default values without exceptions
- Debug logging can be disabled in production builds

## Common Parameter Codes

| Parameter Code | Type | Default | Description |
|----------------|------|---------|-------------|
| `CURRENCY` | String | "USD" | Default currency code |
| `DECIMALS` | Integer | 2 | Decimal places for amounts |
| `TAX_RATE` | Decimal | 0.0 | Default tax percentage |
| `DATE_FORMAT` | String | "MM/dd/yyyy" | Date display format |
| `PAGE_SIZE` | Integer | 50 | Default grid page size |
| `ENABLE_LOGGING` | Boolean | true | Enable debug logging |
| `FISCAL_YEAR_START` | DateTime | "04/01" | Fiscal year start date |
| `MAX_DISCOUNT` | Decimal | 10.0 | Maximum discount percentage |

This comprehensive parameter system provides type-safe, performant access to application configuration while maintaining full backward compatibility with existing code.
