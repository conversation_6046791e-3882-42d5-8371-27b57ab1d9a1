// CurrencyFormattingService - Centralized currency formatting service
// Usage: Provides consistent currency formatting throughout the application

using System;
using System.Diagnostics;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.Data;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Centralized service for currency formatting operations
    /// Ensures consistent currency display across all forms and grids
    /// </summary>
    public static class CurrencyFormattingService
    {
        #region Grid Currency Formatting

        /// <summary>
        /// Applies currency formatting to specific grid columns
        /// </summary>
        /// <param name="gridView">The grid view to format</param>
        /// <param name="columnNames">Array of column names to format</param>
        public static void ApplyCurrencyFormatting(GridView gridView, params string[] columnNames)
        {
            try
            {
                if (gridView == null || columnNames == null || columnNames.Length == 0)
                {
                    Debug.WriteLine("CurrencyFormattingService.ApplyCurrencyFormatting: Invalid parameters");
                    return;
                }

                Debug.WriteLine($"=== CurrencyFormattingService.ApplyCurrencyFormatting: Starting for {columnNames.Length} columns ===");

                // Get currency formatting parameters using unified parameter manager
                string currencyFormat = UnifiedParameterManager.Instance.Currency.Format;

                foreach (string columnName in columnNames)
                {
                    var column = gridView.Columns[columnName];
                    if (column != null)
                    {
                        column.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                        column.DisplayFormat.FormatString = currencyFormat;
                        Debug.WriteLine($"Applied currency format '{currencyFormat}' to column {columnName}");
                    }
                    else
                    {
                        Debug.WriteLine($"Column '{columnName}' not found in grid view");
                    }
                }

                Debug.WriteLine("=== CurrencyFormattingService.ApplyCurrencyFormatting: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ApplyCurrencyFormatting: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Applies currency formatting to two specific columns (overload for backwards compatibility)
        /// </summary>
        /// <param name="gridView">The grid view to format</param>
        /// <param name="oeColumnName">First column name (typically OE price column)</param>
        /// <param name="afmColumnName">Second column name (typically AFM price column)</param>
        public static void ApplyCurrencyFormatting(GridView gridView, string oeColumnName, string afmColumnName)
        {
            ApplyCurrencyFormatting(gridView, oeColumnName, afmColumnName);
        }

        /// <summary>
        /// Applies currency formatting to all price-related columns in a grid
        /// Automatically detects columns with "Price", "Amount", "Cost", "Total" in their names
        /// </summary>
        /// <param name="gridView">The grid view to format</param>
        public static void ApplyAutoCurrencyFormatting(GridView gridView)
        {
            try
            {
                if (gridView == null)
                {
                    Debug.WriteLine("CurrencyFormattingService.ApplyAutoCurrencyFormatting: GridView is null");
                    return;
                }

                Debug.WriteLine("=== CurrencyFormattingService.ApplyAutoCurrencyFormatting: Starting ===");

                string currencyFormat = UnifiedParameterManager.Instance.Currency.Format;
                int formattedColumns = 0;

                foreach (GridColumn column in gridView.Columns)
                {
                    if (IsCurrencyColumn(column.FieldName))
                    {
                        column.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                        column.DisplayFormat.FormatString = currencyFormat;
                        formattedColumns++;
                        Debug.WriteLine($"Auto-applied currency format to column {column.FieldName}");
                    }
                }

                Debug.WriteLine($"=== CurrencyFormattingService.ApplyAutoCurrencyFormatting: Completed, formatted {formattedColumns} columns ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ApplyAutoCurrencyFormatting: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Applies currency summary formatting to grid columns
        /// </summary>
        /// <param name="gridView">The grid view to format</param>
        /// <param name="columnNames">Array of column names to add currency summaries</param>
        /// <param name="summaryType">Type of summary (default: Sum)</param>
        public static void ApplyCurrencySummaries(GridView gridView, string[] columnNames, SummaryItemType summaryType = SummaryItemType.Sum)
        {
            try
            {
                if (gridView == null || columnNames == null || columnNames.Length == 0)
                {
                    Debug.WriteLine("CurrencyFormattingService.ApplyCurrencySummaries: Invalid parameters");
                    return;
                }

                Debug.WriteLine($"=== CurrencyFormattingService.ApplyCurrencySummaries: Starting for {columnNames.Length} columns ===");

                // Get currency summary format using unified parameter manager
                string summaryFormat = UnifiedParameterManager.Instance.Currency.Format;

                foreach (string columnName in columnNames)
                {
                    var column = gridView.Columns[columnName];
                    if (column != null)
                    {
                        // Clear existing summaries for this column
                        column.Summary.Clear();

                        // Add currency summary
                        var summary = new DevExpress.XtraGrid.GridColumnSummaryItem(summaryType, columnName, summaryFormat);
                        column.Summary.Add(summary);
                        Debug.WriteLine($"Applied currency summary to column {columnName}");
                    }
                    else
                    {
                        Debug.WriteLine($"Column '{columnName}' not found in grid view");
                    }
                }

                // Enable footer to show summaries
                gridView.OptionsView.ShowFooter = true;

                Debug.WriteLine("=== CurrencyFormattingService.ApplyCurrencySummaries: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ApplyCurrencySummaries: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Applies complete currency formatting (display format + summaries) to grid columns
        /// </summary>
        /// <param name="gridView">The grid view to format</param>
        /// <param name="columnNames">Array of column names to format</param>
        public static void ApplyCompleteCurrencyFormatting(GridView gridView, params string[] columnNames)
        {
            try
            {
                Debug.WriteLine("=== CurrencyFormattingService.ApplyCompleteCurrencyFormatting: Starting ===");

                // Apply display formatting
                ApplyCurrencyFormatting(gridView, columnNames);

                // Apply summaries
                ApplyCurrencySummaries(gridView, columnNames);

                Debug.WriteLine("=== CurrencyFormattingService.ApplyCompleteCurrencyFormatting: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ApplyCompleteCurrencyFormatting: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Determines if a column name indicates it should have currency formatting
        /// </summary>
        /// <param name="columnName">The column name to check</param>
        /// <returns>True if the column should have currency formatting</returns>
        private static bool IsCurrencyColumn(string columnName)
        {
            if (string.IsNullOrWhiteSpace(columnName))
                return false;

            string lowerColumnName = columnName.ToLowerInvariant();

            return lowerColumnName.Contains("price") ||
                   lowerColumnName.Contains("amount") ||
                   lowerColumnName.Contains("cost") ||
                   lowerColumnName.Contains("total") ||
                   lowerColumnName.Contains("value") ||
                   lowerColumnName.Contains("fee") ||
                   lowerColumnName.Contains("charge") ||
                   lowerColumnName.Contains("rate") && !lowerColumnName.Contains("taxrate") && !lowerColumnName.Contains("discountrate");
        }

        /// <summary>
        /// Gets the current currency symbol for display
        /// </summary>
        /// <returns>Currency symbol</returns>
        public static string GetCurrencySymbol()
        {
            try
            {
                return UnifiedParameterManager.Instance.Currency.Symbol;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting currency symbol: {ex.Message}");
                return "USD";
            }
        }

        /// <summary>
        /// Gets the current decimal places for currency formatting
        /// </summary>
        /// <returns>Number of decimal places</returns>
        public static int GetCurrencyDecimalPlaces()
        {
            try
            {
                return UnifiedParameterManager.Instance.Currency.DecimalPlaces;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting currency decimal places: {ex.Message}");
                return 2;
            }
        }

        /// <summary>
        /// Formats a decimal value as currency string
        /// </summary>
        /// <param name="value">The value to format</param>
        /// <param name="includeSymbol">Whether to include currency symbol</param>
        /// <returns>Formatted currency string</returns>
        public static string FormatCurrency(decimal value, bool includeSymbol = true)
        {
            try
            {
                string currency = GetCurrencySymbol();
                int decimals = GetCurrencyDecimalPlaces();

                if (includeSymbol)
                {
                    return $"{currency} {value.ToString($"N{decimals}")}";
                }
                else
                {
                    return value.ToString($"N{decimals}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error formatting currency: {ex.Message}");
                return value.ToString("N2");
            }
        }

        #endregion

        #region Refresh Methods

        /// <summary>
        /// Refreshes currency formatting for a grid view
        /// Call this method when currency parameters have been updated
        /// </summary>
        /// <param name="gridView">The grid view to refresh</param>
        public static void RefreshGridCurrencyFormatting(GridView gridView)
        {
            try
            {
                Debug.WriteLine("=== CurrencyFormattingService.RefreshGridCurrencyFormatting: Starting ===");

                if (gridView == null)
                {
                    Debug.WriteLine("GridView is null, cannot refresh formatting");
                    return;
                }

                // Refresh parameters first using unified parameter manager
                UnifiedParameterManager.Instance.RefreshFromDatabase();

                // Re-apply auto currency formatting
                ApplyAutoCurrencyFormatting(gridView);

                // Refresh the grid display
                gridView.RefreshData();

                Debug.WriteLine("=== CurrencyFormattingService.RefreshGridCurrencyFormatting: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing grid currency formatting: {ex.Message}");
            }
        }

        #endregion
    }
}
