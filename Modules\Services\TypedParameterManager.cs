// TypedParameterManager - Enhanced parameter management with type-aware retrieval
// Usage: Provides type-safe parameter access with automatic conversion and validation

using System;
using System.Diagnostics;
using System.Globalization;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Enhanced parameter manager that provides type-aware parameter retrieval
    /// Builds on ParameterCacheService to provide intelligent type conversion and validation
    /// </summary>
    public static class TypedParameterManager
    {
        #region Currency and Formatting Parameters

        /// <summary>
        /// Gets the currency code from parameters
        /// </summary>
        /// <param name="defaultValue">Default currency code if parameter not found</param>
        /// <returns>Currency code (e.g., "AED", "USD")</returns>
        public static string GetCurrency(string defaultValue = "AED")
        {
            try
            {
                string currency = ParameterCacheService.Instance.GetParameter("CURRENCY", defaultValue);
                Debug.WriteLine($"TypedParameterManager.GetCurrency: {currency}");
                return currency;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting currency parameter: {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// Gets the decimal places for currency formatting
        /// </summary>
        /// <param name="defaultValue">Default decimal places if parameter not found</param>
        /// <returns>Number of decimal places</returns>
        public static int GetCurrencyDecimals(int defaultValue = 2)
        {
            try
            {
                int decimals = ParameterCacheService.Instance.GetInt("DECIMALS", defaultValue);
                Debug.WriteLine($"TypedParameterManager.GetCurrencyDecimals: {decimals}");
                return decimals;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting currency decimals parameter: {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// Gets a formatted currency string for display formatting
        /// </summary>
        /// <param name="includeSymbol">Whether to include currency symbol in format</param>
        /// <returns>Currency format string (e.g., "AED {0:N2}" or "{0:N2}")</returns>
        public static string GetCurrencyFormat(bool includeSymbol = true)
        {
            try
            {
                string currency = GetCurrency();
                int decimals = GetCurrencyDecimals();
                
                string format = includeSymbol ? $"{currency} {{0:N{decimals}}}" : $"{{0:N{decimals}}}";
                Debug.WriteLine($"TypedParameterManager.GetCurrencyFormat: {format}");
                return format;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting currency format: {ex.Message}");
                return includeSymbol ? "AED {0:N2}" : "{0:N2}";
            }
        }

        /// <summary>
        /// Gets a currency summary format for grid totals
        /// </summary>
        /// <param name="prefix">Prefix text (e.g., "Total: ")</param>
        /// <returns>Currency summary format string</returns>
        public static string GetCurrencySummaryFormat(string prefix = "Total: ")
        {
            try
            {
                string currency = GetCurrency();
                int decimals = GetCurrencyDecimals();
                
                string format = $"{prefix}{currency} {{0:N{decimals}}}";
                Debug.WriteLine($"TypedParameterManager.GetCurrencySummaryFormat: {format}");
                return format;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting currency summary format: {ex.Message}");
                return $"{prefix}AED {{0:N2}}";
            }
        }

        #endregion

        #region Company Information Parameters

        /// <summary>
        /// Gets the company name from parameters
        /// </summary>
        /// <param name="defaultValue">Default company name if parameter not found</param>
        /// <returns>Company name</returns>
        public static string GetCompanyName(string defaultValue = "ProManage")
        {
            try
            {
                string companyName = ParameterCacheService.Instance.GetParameter("COMPANY_NAME", defaultValue);
                Debug.WriteLine($"TypedParameterManager.GetCompanyName: {companyName}");
                return companyName;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting company name parameter: {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// Gets the company address from parameters
        /// </summary>
        /// <param name="defaultValue">Default company address if parameter not found</param>
        /// <returns>Company address</returns>
        public static string GetCompanyAddress(string defaultValue = "")
        {
            try
            {
                string address = ParameterCacheService.Instance.GetParameter("COMPANY_ADDRESS", defaultValue);
                Debug.WriteLine($"TypedParameterManager.GetCompanyAddress: {address}");
                return address;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting company address parameter: {ex.Message}");
                return defaultValue;
            }
        }

        #endregion

        #region Business Logic Parameters

        /// <summary>
        /// Gets the tax rate from parameters
        /// </summary>
        /// <param name="defaultValue">Default tax rate if parameter not found</param>
        /// <returns>Tax rate as decimal (e.g., 0.05 for 5%)</returns>
        public static decimal GetTaxRate(decimal defaultValue = 0.05m)
        {
            try
            {
                decimal taxRate = ParameterCacheService.Instance.GetDecimal("TAX_RATE", defaultValue);
                Debug.WriteLine($"TypedParameterManager.GetTaxRate: {taxRate}");
                return taxRate;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting tax rate parameter: {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// Gets whether GST should be shown/calculated
        /// </summary>
        /// <param name="defaultValue">Default GST visibility if parameter not found</param>
        /// <returns>True if GST should be shown</returns>
        public static bool GetShowGST(bool defaultValue = false)
        {
            try
            {
                bool showGST = ParameterCacheService.Instance.GetBool("SHOW_GST", defaultValue);
                Debug.WriteLine($"TypedParameterManager.GetShowGST: {showGST}");
                return showGST;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting show GST parameter: {ex.Message}");
                return defaultValue;
            }
        }

        #endregion

        #region Date and Time Parameters

        /// <summary>
        /// Gets the date format from parameters
        /// </summary>
        /// <param name="defaultValue">Default date format if parameter not found</param>
        /// <returns>Date format string</returns>
        public static string GetDateFormat(string defaultValue = "dd-MM-yyyy")
        {
            try
            {
                string dateFormat = ParameterCacheService.Instance.GetParameter("DATE_FORMAT", defaultValue);
                Debug.WriteLine($"TypedParameterManager.GetDateFormat: {dateFormat}");
                return dateFormat;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting date format parameter: {ex.Message}");
                return defaultValue;
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Refreshes the parameter cache to pick up any changes
        /// </summary>
        /// <returns>True if refresh successful</returns>
        public static bool RefreshParameters()
        {
            try
            {
                Debug.WriteLine("TypedParameterManager.RefreshParameters: Refreshing parameter cache");
                bool result = ParameterCacheService.Instance.RefreshCache();
                Debug.WriteLine($"TypedParameterManager.RefreshParameters: Result = {result}");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing parameters: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Checks if the parameter cache is loaded and ready
        /// </summary>
        /// <returns>True if parameter cache is loaded</returns>
        public static bool IsParameterCacheReady()
        {
            try
            {
                bool isReady = ParameterCacheService.Instance.IsLoaded;
                Debug.WriteLine($"TypedParameterManager.IsParameterCacheReady: {isReady}");
                return isReady;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking parameter cache status: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
