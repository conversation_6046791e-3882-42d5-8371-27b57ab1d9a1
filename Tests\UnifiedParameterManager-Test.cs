// UnifiedParameterManager Test - Comprehensive validation of unified parameter management system
// Usage: Test file to verify the new unified parameter manager works correctly and replaces old services

using System;
using System.Diagnostics;
using ProManage.Modules.Services;

namespace ProManage.Tests
{
    /// <summary>
    /// Test class for verifying UnifiedParameterManager functionality
    /// This class provides methods to test the unified parameter management system
    /// </summary>
    public static class UnifiedParameterManagerTest
    {
        /// <summary>
        /// Runs comprehensive tests for all unified parameter manager functionality
        /// </summary>
        public static void RunAllTests()
        {
            Debug.WriteLine("=== UnifiedParameterManager Test Suite Starting ===");

            try
            {
                // Test initialization
                TestInitialization();

                // Test basic parameter access
                TestBasicParameterAccess();

                // Test typed parameter access
                TestTypedParameterAccess();

                // Test category-based access
                TestCategoryAccess();

                // Test error handling
                TestErrorHandling();

                // Test performance
                TestPerformance();

                Debug.WriteLine("=== UnifiedParameterManager Test Suite Completed Successfully ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"=== UnifiedParameterManager Test Suite Failed: {ex.Message} ===");
                throw;
            }
        }

        /// <summary>
        /// Tests the initialization functionality
        /// </summary>
        private static void TestInitialization()
        {
            Debug.WriteLine("--- Testing Initialization ---");

            // Test singleton instance
            var instance1 = UnifiedParameterManager.Instance;
            var instance2 = UnifiedParameterManager.Instance;
            
            if (instance1 != instance2)
                throw new Exception("Singleton pattern failed - different instances returned");

            Debug.WriteLine("✓ Singleton pattern working correctly");

            // Test initialization
            bool initialized = UnifiedParameterManager.Instance.Initialize();
            Debug.WriteLine($"✓ Initialization result: {initialized}");

            // Test parameter count
            int parameterCount = UnifiedParameterManager.Instance.ParameterCount;
            Debug.WriteLine($"✓ Parameter count: {parameterCount}");

            if (parameterCount == 0)
                Debug.WriteLine("⚠ Warning: No parameters loaded - check database connection");

            Debug.WriteLine("--- Initialization Tests Completed ---");
        }

        /// <summary>
        /// Tests basic string parameter access
        /// </summary>
        private static void TestBasicParameterAccess()
        {
            Debug.WriteLine("--- Testing Basic Parameter Access ---");

            // Test string parameter access
            string currency = UnifiedParameterManager.Instance.GetString("CURRENCY", "USD");
            Debug.WriteLine($"✓ Currency parameter: {currency}");

            // Test parameter existence check
            bool hasCurrency = UnifiedParameterManager.Instance.HasParameter("CURRENCY");
            Debug.WriteLine($"✓ Has CURRENCY parameter: {hasCurrency}");

            // Test non-existent parameter
            string nonExistent = UnifiedParameterManager.Instance.GetString("NON_EXISTENT_PARAM", "DEFAULT");
            if (nonExistent != "DEFAULT")
                throw new Exception("Default value not returned for non-existent parameter");

            Debug.WriteLine($"✓ Non-existent parameter returns default: {nonExistent}");

            Debug.WriteLine("--- Basic Parameter Access Tests Completed ---");
        }

        /// <summary>
        /// Tests typed parameter access methods
        /// </summary>
        private static void TestTypedParameterAccess()
        {
            Debug.WriteLine("--- Testing Typed Parameter Access ---");

            // Test integer access
            int decimals = UnifiedParameterManager.Instance.GetInt("DECIMALS", 2);
            Debug.WriteLine($"✓ Decimals (int): {decimals}");

            // Test boolean access
            bool showGST = UnifiedParameterManager.Instance.GetBool("SHOW_GST", false);
            Debug.WriteLine($"✓ Show GST (bool): {showGST}");

            // Test decimal access
            decimal taxRate = UnifiedParameterManager.Instance.GetDecimal("TAX_RATE", 0.05m);
            Debug.WriteLine($"✓ Tax Rate (decimal): {taxRate}");

            // Test DateTime access
            DateTime testDate = UnifiedParameterManager.Instance.GetDateTime("TEST_DATE", DateTime.Now);
            Debug.WriteLine($"✓ Test Date (DateTime): {testDate}");

            // Test generic parameter access
            string genericCurrency = UnifiedParameterManager.Instance.GetParameter<string>("CURRENCY", "USD");
            Debug.WriteLine($"✓ Generic currency access: {genericCurrency}");

            Debug.WriteLine("--- Typed Parameter Access Tests Completed ---");
        }

        /// <summary>
        /// Tests category-based parameter access
        /// </summary>
        private static void TestCategoryAccess()
        {
            Debug.WriteLine("--- Testing Category Access ---");

            // Test Currency category
            var currency = UnifiedParameterManager.Instance.Currency;
            Debug.WriteLine($"✓ Currency Symbol: {currency.Symbol}");
            Debug.WriteLine($"✓ Currency Decimal Places: {currency.DecimalPlaces}");
            Debug.WriteLine($"✓ Currency Format: {currency.Format}");
            Debug.WriteLine($"✓ Currency Decimal Format: {currency.DecimalFormat}");

            // Test Company category
            var company = UnifiedParameterManager.Instance.Company;
            Debug.WriteLine($"✓ Company Name: {company.Name}");
            Debug.WriteLine($"✓ Company Address: {company.Address}");
            Debug.WriteLine($"✓ Company Phone: {company.Phone}");
            Debug.WriteLine($"✓ Company Email: {company.Email}");
            Debug.WriteLine($"✓ Company Website: {company.Website}");

            // Test UI category
            var ui = UnifiedParameterManager.Instance.UI;
            Debug.WriteLine($"✓ Show Tooltips: {ui.ShowTooltips}");
            Debug.WriteLine($"✓ Default Page Size: {ui.DefaultPageSize}");
            Debug.WriteLine($"✓ Enable Debug Logging: {ui.EnableDebugLogging}");
            Debug.WriteLine($"✓ Theme: {ui.Theme}");
            Debug.WriteLine($"✓ Date Format: {ui.DateFormat}");

            // Test Business category
            var business = UnifiedParameterManager.Instance.Business;
            Debug.WriteLine($"✓ Tax Rate: {business.TaxRate}");
            Debug.WriteLine($"✓ Max Discount Percent: {business.MaxDiscountPercent}");
            Debug.WriteLine($"✓ Allow Negative Amounts: {business.AllowNegativeAmounts}");
            Debug.WriteLine($"✓ Session Timeout Minutes: {business.SessionTimeoutMinutes}");
            Debug.WriteLine($"✓ Show GST: {business.ShowGST}");

            Debug.WriteLine("--- Category Access Tests Completed ---");
        }

        /// <summary>
        /// Tests error handling and edge cases
        /// </summary>
        private static void TestErrorHandling()
        {
            Debug.WriteLine("--- Testing Error Handling ---");

            // Test null/empty parameter codes
            string nullResult = UnifiedParameterManager.Instance.GetString(null, "DEFAULT");
            if (nullResult != "DEFAULT")
                throw new Exception("Null parameter code should return default");

            string emptyResult = UnifiedParameterManager.Instance.GetString("", "DEFAULT");
            if (emptyResult != "DEFAULT")
                throw new Exception("Empty parameter code should return default");

            Debug.WriteLine("✓ Null/empty parameter codes handled correctly");

            // Test invalid type conversions
            int invalidInt = UnifiedParameterManager.Instance.GetInt("CURRENCY", 999);
            Debug.WriteLine($"✓ Invalid int conversion returns default: {invalidInt}");

            bool invalidBool = UnifiedParameterManager.Instance.GetBool("CURRENCY", true);
            Debug.WriteLine($"✓ Invalid bool conversion returns default: {invalidBool}");

            Debug.WriteLine("--- Error Handling Tests Completed ---");
        }

        /// <summary>
        /// Tests performance characteristics
        /// </summary>
        private static void TestPerformance()
        {
            Debug.WriteLine("--- Testing Performance ---");

            const int iterations = 1000;
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Test parameter access performance
            for (int i = 0; i < iterations; i++)
            {
                string currency = UnifiedParameterManager.Instance.GetString("CURRENCY", "USD");
                int decimals = UnifiedParameterManager.Instance.GetInt("DECIMALS", 2);
                bool showGST = UnifiedParameterManager.Instance.GetBool("SHOW_GST", false);
            }

            stopwatch.Stop();
            double avgTimePerAccess = (double)stopwatch.ElapsedMilliseconds / (iterations * 3);

            Debug.WriteLine($"✓ {iterations * 3} parameter accesses completed in {stopwatch.ElapsedMilliseconds}ms");
            Debug.WriteLine($"✓ Average time per parameter access: {avgTimePerAccess:F4}ms");

            if (avgTimePerAccess > 1.0)
                Debug.WriteLine("⚠ Warning: Parameter access time exceeds 1ms - consider optimization");

            // Test category access performance
            stopwatch.Restart();
            for (int i = 0; i < iterations; i++)
            {
                string format = UnifiedParameterManager.Instance.Currency.Format;
                string companyName = UnifiedParameterManager.Instance.Company.Name;
                bool showTooltips = UnifiedParameterManager.Instance.UI.ShowTooltips;
            }
            stopwatch.Stop();

            double avgCategoryTime = (double)stopwatch.ElapsedMilliseconds / (iterations * 3);
            Debug.WriteLine($"✓ {iterations * 3} category accesses completed in {stopwatch.ElapsedMilliseconds}ms");
            Debug.WriteLine($"✓ Average time per category access: {avgCategoryTime:F4}ms");

            Debug.WriteLine("--- Performance Tests Completed ---");
        }

        /// <summary>
        /// Tests migration from old services - validates that UnifiedParameterManager provides same functionality
        /// </summary>
        public static void TestMigrationCompatibility()
        {
            Debug.WriteLine("--- Testing Migration Compatibility ---");

            try
            {
                // Test that UnifiedParameterManager provides same functionality as old services
                
                // Currency functionality (replaces CurrencyFormattingService)
                string currencySymbol = UnifiedParameterManager.Instance.Currency.Symbol;
                int decimalPlaces = UnifiedParameterManager.Instance.Currency.DecimalPlaces;
                string currencyFormat = UnifiedParameterManager.Instance.Currency.Format;
                
                Debug.WriteLine($"✓ Currency Symbol (was CurrencyFormattingService.GetCurrencySymbol): {currencySymbol}");
                Debug.WriteLine($"✓ Decimal Places (was CurrencyFormattingService.GetCurrencyDecimalPlaces): {decimalPlaces}");
                Debug.WriteLine($"✓ Currency Format (was TypedParameterManager.GetCurrencyFormat): {currencyFormat}");

                // Company functionality (replaces TypedParameterManager)
                string companyName = UnifiedParameterManager.Instance.Company.Name;
                string companyAddress = UnifiedParameterManager.Instance.Company.Address;
                
                Debug.WriteLine($"✓ Company Name (was TypedParameterManager.GetCompanyName): {companyName}");
                Debug.WriteLine($"✓ Company Address (was TypedParameterManager.GetCompanyAddress): {companyAddress}");

                // Business functionality (replaces TypedParameterManager)
                decimal taxRate = UnifiedParameterManager.Instance.Business.TaxRate;
                bool showGST = UnifiedParameterManager.Instance.Business.ShowGST;
                
                Debug.WriteLine($"✓ Tax Rate (was TypedParameterManager.GetTaxRate): {taxRate}");
                Debug.WriteLine($"✓ Show GST (was TypedParameterManager.GetShowGST): {showGST}");

                // Typed access functionality (replaces ParameterCacheServiceExtensions)
                int intValue = UnifiedParameterManager.Instance.GetInt("DECIMALS", 2);
                bool boolValue = UnifiedParameterManager.Instance.GetBool("SHOW_GST", false);
                decimal decimalValue = UnifiedParameterManager.Instance.GetDecimal("TAX_RATE", 0.05m);
                
                Debug.WriteLine($"✓ Int Access (was ParameterCacheServiceExtensions.GetInt): {intValue}");
                Debug.WriteLine($"✓ Bool Access (was ParameterCacheServiceExtensions.GetBool): {boolValue}");
                Debug.WriteLine($"✓ Decimal Access (was ParameterCacheServiceExtensions.GetDecimal): {decimalValue}");

                Debug.WriteLine("✓ Migration compatibility verified - all old service functionality available");
                Debug.WriteLine("--- Migration Compatibility Tests Completed ---");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"✗ Migration compatibility test failed: {ex.Message}");
                throw;
            }
        }
    }
}
