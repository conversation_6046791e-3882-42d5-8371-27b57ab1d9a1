// UI helpers for EstimateForm including grid management, navigation, and form state management
// <PERSON>les all complex UI operations to keep the main form clean and focused

using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors;
using ProManage.Modules.Models.EstimateForm;
using ProManage.Modules.Data.EstimateForm;
using ProManage.Modules.Services;
using ProManage.Modules.UI;
using ProManage.Modules.Connections;

namespace ProManage.Modules.Helpers.EstimateForm
{
    /// <summary>
    /// Helper class for EstimateForm UI operations, grid management, and navigation
    /// </summary>
    public static class EstimateFormHelper
    {
        #region Grid Management

        /// <summary>
        /// Sets up the grid data table with proper columns and data types
        /// </summary>
        /// <param name="gridControl">The grid control to setup</param>
        /// <param name="gridView">The grid view to configure</param>
        /// <returns>Configured DataTable for the grid</returns>
        public static DataTable SetupGridDataTable(GridControl gridControl, GridView gridView)
        {
            try
            {
                Debug.WriteLine("=== EstimateFormHelper.SetupGridDataTable: Starting ===");

                var gridDataTable = new DataTable();
                gridDataTable.Columns.Add("SerialNumber", typeof(int));
                gridDataTable.Columns.Add("PartNumber", typeof(string));
                gridDataTable.Columns.Add("Description", typeof(string));
                gridDataTable.Columns.Add("Quantity", typeof(decimal));
                gridDataTable.Columns.Add("OEPrice", typeof(decimal));
                gridDataTable.Columns.Add("AFMPrice", typeof(decimal));
                gridDataTable.Columns.Add("Remarks", typeof(string));
                gridDataTable.Columns.Add("Status", typeof(bool));
                // Note: Delete column is handled as a button column, not a data column

                Debug.WriteLine($"Created DataTable with {gridDataTable.Columns.Count} columns");

                // Set the data source FIRST
                gridControl.DataSource = gridDataTable;
                Debug.WriteLine("GridControl.DataSource set to gridDataTable");

                // Configure GridView columns AFTER setting data source
                SetupGridViewColumns(gridView);

                Debug.WriteLine("=== EstimateFormHelper.SetupGridDataTable: Completed ===");
                return gridDataTable;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in SetupGridDataTable: {ex.Message}");
                throw new Exception($"Error setting up grid: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Configures the grid view columns with proper formatting and properties
        /// </summary>
        /// <param name="gridView">The grid view to configure</param>
        public static void SetupGridViewColumns(GridView gridView)
        {
            try
            {
                Debug.WriteLine("=== EstimateFormHelper.SetupGridViewColumns: Starting ===");

                // Clear any existing columns
                gridView.Columns.Clear();
                Debug.WriteLine("Cleared existing columns");

                // Serial Number column (read-only)
                var colSerial = gridView.Columns.AddField("SerialNumber");
                colSerial.Caption = "S.No";
                colSerial.Width = 60;
                colSerial.OptionsColumn.AllowEdit = false;
                colSerial.OptionsColumn.ReadOnly = true;
                colSerial.Visible = true;

                // Part Number column
                var colPartNo = gridView.Columns.AddField("PartNumber");
                colPartNo.Caption = "Part Number";
                colPartNo.Width = 120;
                colPartNo.OptionsColumn.AllowEdit = true;
                colPartNo.Visible = true;

                // Description column
                var colDescription = gridView.Columns.AddField("Description");
                colDescription.Caption = "Description";
                colDescription.Width = 200;
                colDescription.OptionsColumn.AllowEdit = true;
                colDescription.Visible = true;

                // Quantity column
                var colQuantity = gridView.Columns.AddField("Quantity");
                colQuantity.Caption = "Quantity";
                colQuantity.Width = 80;
                colQuantity.OptionsColumn.AllowEdit = true;
                colQuantity.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                colQuantity.DisplayFormat.FormatString = "n2";
                colQuantity.Visible = true;

                // OE Price column
                var colOEPrice = gridView.Columns.AddField("OEPrice");
                colOEPrice.Caption = "OE Price";
                colOEPrice.Width = 100;
                colOEPrice.OptionsColumn.AllowEdit = true;
                colOEPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                colOEPrice.Visible = true;

                // AFM Price column
                var colAFMPrice = gridView.Columns.AddField("AFMPrice");
                colAFMPrice.Caption = "AFM Price";
                colAFMPrice.Width = 100;
                colAFMPrice.OptionsColumn.AllowEdit = true;
                colAFMPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                colAFMPrice.Visible = true;

                // Apply currency formatting using centralized service
                ProManage.Modules.Services.CurrencyFormattingService.ApplyCurrencyFormatting(gridView, "OEPrice", "AFMPrice");

                // Remarks column
                var colRemarks = gridView.Columns.AddField("Remarks");
                colRemarks.Caption = "Remarks";
                colRemarks.Width = 150;
                colRemarks.OptionsColumn.AllowEdit = true;
                colRemarks.Visible = true;

                // Status column (checkbox) - positioned before Delete column
                var colStatus = gridView.Columns.AddField("Status");
                colStatus.Caption = "Status";
                colStatus.Width = 80;
                colStatus.OptionsColumn.AllowEdit = true;
                colStatus.Visible = true;

                // Create repository item for checkbox
                var checkEditRepository = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
                checkEditRepository.ValueChecked = true;
                checkEditRepository.ValueUnchecked = false;
                checkEditRepository.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked;

                // Assign repository to Status column
                colStatus.ColumnEdit = checkEditRepository;

                // Delete button column (unbound column)
                var colDelete = gridView.Columns.AddVisible("Delete");
                colDelete.Caption = "Delete";
                colDelete.Width = 80;
                colDelete.OptionsColumn.AllowEdit = false;
                colDelete.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
                colDelete.OptionsColumn.AllowMove = false;
                colDelete.OptionsColumn.AllowSize = false;
                colDelete.UnboundType = DevExpress.Data.UnboundColumnType.Object;
                colDelete.Visible = true;

                // Create repository item for delete button
                var deleteButtonRepository = new DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit();
                deleteButtonRepository.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor;
                deleteButtonRepository.Buttons.Clear();
                deleteButtonRepository.Buttons.Add(new DevExpress.XtraEditors.Controls.EditorButton(
                    DevExpress.XtraEditors.Controls.ButtonPredefines.Delete, "Delete", -1, true, true, false,
                    DevExpress.XtraEditors.ImageLocation.MiddleCenter, null));

                // Assign repository to column
                colDelete.ColumnEdit = deleteButtonRepository;

                // Configure grid view options
                gridView.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
                gridView.OptionsView.ShowNewItemRow = false;
                gridView.OptionsBehavior.Editable = true;
                gridView.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
                gridView.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.True;
                gridView.OptionsView.ShowColumnHeaders = true;
                gridView.OptionsView.ColumnAutoWidth = false;

                Debug.WriteLine($"Grid setup completed. Total columns: {gridView.Columns.Count}");
                gridView.BestFitColumns();

                // Setup grid enhancements (formatting, validation, totals)
                SetupGridEnhancements(gridView);

                Debug.WriteLine("=== EstimateFormHelper.SetupGridViewColumns: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in SetupGridViewColumns: {ex.Message}");
                throw new Exception($"Error setting up grid columns: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Loads estimate details into the grid
        /// </summary>
        /// <param name="gridDataTable">The grid data table</param>
        /// <param name="estimateId">The estimate ID to load details for</param>
        public static void LoadEstimateDetailsToGrid(DataTable gridDataTable, int estimateId)
        {
            try
            {
                Debug.WriteLine($"=== LoadEstimateDetailsToGrid: Loading details for estimate ID {estimateId} ===");

                // Clear existing data
                gridDataTable.Clear();

                // Get details from repository
                var details = EstimateFormRepository.GetEstimateDetailsById(estimateId);
                Debug.WriteLine($"Retrieved {details.Count} detail records from repository");

                // Add details to grid
                int serialNumber = 1;
                foreach (var detail in details)
                {
                    var row = gridDataTable.NewRow();
                    row["SerialNumber"] = serialNumber++;
                    row["PartNumber"] = detail.PartNo ?? "";
                    row["Description"] = detail.Description ?? "";
                    row["Quantity"] = detail.Qty ?? 0;
                    row["OEPrice"] = detail.OEPrice ?? 0;
                    row["AFMPrice"] = detail.AFMPrice ?? 0;
                    row["Remarks"] = detail.Remarks ?? "";
                    row["Status"] = detail.ApproveStatus ?? false;

                    gridDataTable.Rows.Add(row);
                    Debug.WriteLine($"Added row {serialNumber - 1}: {detail.PartNo} - {detail.Description} - Status: {detail.ApproveStatus}");
                }

                Debug.WriteLine($"=== LoadEstimateDetailsToGrid: Loaded {details.Count} details successfully ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in LoadEstimateDetailsToGrid: {ex.Message}");
                throw new Exception($"Error loading estimate details to grid: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Refreshes the grid display
        /// </summary>
        /// <param name="gridControl">The grid control to refresh</param>
        /// <param name="gridView">The grid view to refresh</param>
        public static void RefreshGridDisplay(GridControl gridControl, GridView gridView)
        {
            try
            {
                Debug.WriteLine("=== RefreshGridDisplay: Starting ===");

                gridControl.RefreshDataSource();
                gridView.RefreshData();
                gridView.LayoutChanged();

                Debug.WriteLine("=== RefreshGridDisplay: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in RefreshGridDisplay: {ex.Message}");
                // Don't throw - this is not critical
            }
        }

        /// <summary>
        /// Ensures grid columns are visible even when there's no data
        /// </summary>
        /// <param name="gridDataTable">The grid data table</param>
        /// <param name="gridView">The grid view</param>
        /// <param name="gridControl">The grid control</param>
        public static void EnsureGridColumnsVisible(DataTable gridDataTable, GridView gridView, GridControl gridControl)
        {
            try
            {
                Debug.WriteLine("=== EnsureGridColumnsVisible: Starting ===");

                // First, make sure columns are set up
                if (gridView.Columns.Count == 0)
                {
                    Debug.WriteLine("No columns found, setting up columns first");
                    SetupGridViewColumns(gridView);
                }

                // Force column headers to be visible
                gridView.OptionsView.ShowColumnHeaders = true;

                // Add a dummy row to make columns visible, then remove it
                if (gridDataTable != null && gridDataTable.Rows.Count == 0)
                {
                    Debug.WriteLine("Adding dummy row to make columns visible");
                    var dummyRow = gridDataTable.NewRow();
                    dummyRow["SerialNumber"] = 1;
                    dummyRow["PartNumber"] = "";
                    dummyRow["Description"] = "";
                    dummyRow["Quantity"] = 0;
                    dummyRow["OEPrice"] = 0.00m;
                    dummyRow["AFMPrice"] = 0.00m;
                    dummyRow["Remarks"] = "";
                    dummyRow["Status"] = false;

                    gridDataTable.Rows.Add(dummyRow);
                    RefreshGridDisplay(gridControl, gridView);
                    gridDataTable.Rows.Clear();

                    Debug.WriteLine("Grid columns made visible with dummy row technique");
                }

                gridView.OptionsView.ShowColumnHeaders = true;
                gridView.Invalidate();

                Debug.WriteLine("=== EnsureGridColumnsVisible: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error ensuring grid columns visible: {ex.Message}");
            }
        }

        /// <summary>
        /// Sets up grid enhancements including formatting, validation, and totals
        /// </summary>
        /// <param name="gridView">The grid view to enhance</param>
        private static void SetupGridEnhancements(GridView gridView)
        {
            try
            {
                Debug.WriteLine("=== SetupGridEnhancements: Starting ===");

                // Setup currency formatting using centralized service
                CurrencyFormattingService.ApplyCurrencyFormatting(gridView, "OEPrice", "AFMPrice");

                // Enable footer to show totals
                gridView.OptionsView.ShowFooter = true;

                // Wire up grid events for real-time enhancements
                SetupGridEvents(gridView);

                Debug.WriteLine("=== SetupGridEnhancements: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in SetupGridEnhancements: {ex.Message}");
                // Don't throw - this is not critical for basic functionality
            }
        }

        /// <summary>
        /// Sets up grid events for validation and formatting
        /// </summary>
        private static void SetupGridEvents(GridView gridView)
        {
            try
            {
                // Remove existing event handlers to avoid duplicates
                gridView.CellValueChanging -= GridView_CellValueChanging;
                gridView.ValidatingEditor -= GridView_ValidatingEditor;
                gridView.CustomUnboundColumnData -= GridView_CustomUnboundColumnData;

                // Add event handlers
                gridView.CellValueChanging += GridView_CellValueChanging;
                gridView.ValidatingEditor += GridView_ValidatingEditor;
                gridView.CustomUnboundColumnData += GridView_CustomUnboundColumnData;

                Debug.WriteLine("Grid events setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up grid events: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles cell value changing - DISABLED uppercase conversion to fix cursor jumping
        /// </summary>
        private static void GridView_CellValueChanging(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            try
            {
                var gridView = sender as GridView;
                if (gridView == null) return;

                // DISABLED: Uppercase conversion was causing cursor to jump to position 1
                // This was making text appear in reverse order (typing "hello" showed as "olleh")
                // SetRowCellValue was interfering with normal text input process

                Debug.WriteLine($"Cell value changing: Column={e.Column?.FieldName}, Value={e.Value}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView_CellValueChanging: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles editor validation for numeric fields
        /// </summary>
        private static void GridView_ValidatingEditor(object sender, DevExpress.XtraEditors.Controls.BaseContainerValidateEditorEventArgs e)
        {
            try
            {
                var gridView = sender as GridView;
                if (gridView == null) return;

                var column = gridView.FocusedColumn;
                if (column == null) return;

                // Validate numeric fields
                if (column.FieldName == "Quantity" || column.FieldName == "OEPrice" || column.FieldName == "AFMPrice")
                {
                    if (e.Value != null && !string.IsNullOrWhiteSpace(e.Value.ToString()))
                    {
                        // Try to parse as decimal for price fields
                        if (column.FieldName == "OEPrice" || column.FieldName == "AFMPrice")
                        {
                            if (!decimal.TryParse(e.Value.ToString(), out decimal decimalValue) || decimalValue < 0)
                            {
                                e.Valid = false;
                                e.ErrorText = "Please enter a valid positive number for price.";
                            }
                        }
                        // Try to parse as integer for quantity
                        else if (column.FieldName == "Quantity")
                        {
                            if (!int.TryParse(e.Value.ToString(), out int intValue) || intValue < 0)
                            {
                                e.Valid = false;
                                e.ErrorText = "Please enter a valid positive integer for quantity.";
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView_ValidatingEditor: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles custom unbound column data for delete button
        /// </summary>
        private static void GridView_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            try
            {
                if (e.Column.FieldName == "Delete" && e.IsGetData)
                {
                    e.Value = "Delete"; // Button text
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView_CustomUnboundColumnData: {ex.Message}");
            }
        }

        #endregion

        #region Form State Management

        /// <summary>
        /// Loads an estimate into the form controls
        /// </summary>
        /// <param name="estimate">The estimate to load</param>
        /// <param name="txtEstimate">Estimate number textbox</param>
        /// <param name="txtCustomer">Customer textbox</param>
        /// <param name="txtVehicle">Vehicle textbox</param>
        /// <param name="txtVIN">VIN textbox</param>
        /// <param name="cbBrand">Brand combobox</param>
        /// <param name="cbLocation">Location combobox</param>
        /// <param name="txtSalesman">Salesman textbox</param>
        /// <param name="dpDocDate">Document date picker</param>
        /// <param name="txtDocRemarks">Document remarks textbox</param>
        /// <param name="gridDataTable">Grid data table</param>
        public static void LoadEstimateToForm(EstimateFormHeaderModel estimate,
            TextEdit txtEstimate, TextEdit txtCustomer, TextEdit txtVehicle, TextEdit txtVIN,
            ComboBoxEdit cbBrand, ComboBoxEdit cbLocation, TextEdit txtSalesman,
            DateEdit dpDocDate, MemoEdit txtDocRemarks, DataTable gridDataTable)
        {
            try
            {
                Debug.WriteLine($"=== LoadEstimateToForm: Loading estimate ID {estimate.Id} ===");

                // Load header data
                txtEstimate.Text = estimate.EstimateNo ?? "";
                txtCustomer.Text = estimate.CustomerName ?? "";
                txtVehicle.Text = estimate.VehicleModel ?? "";
                txtVIN.Text = estimate.VIN ?? "";
                cbBrand.Text = estimate.Brand ?? "";
                cbLocation.Text = estimate.Location ?? "";
                txtSalesman.Text = estimate.SalesmanName ?? "";
                dpDocDate.DateTime = estimate.DocDate ?? DateTime.Now;
                txtDocRemarks.Text = estimate.Remarks ?? "";

                // Load details to grid
                LoadEstimateDetailsToGrid(gridDataTable, estimate.Id);

                Debug.WriteLine($"=== LoadEstimateToForm: Completed for estimate {estimate.EstimateNo} ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in LoadEstimateToForm: {ex.Message}");
                throw new Exception($"Error loading estimate to form: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Clears all form controls
        /// </summary>
        /// <param name="txtEstimate">Estimate number textbox</param>
        /// <param name="txtCustomer">Customer textbox</param>
        /// <param name="txtVehicle">Vehicle textbox</param>
        /// <param name="txtVIN">VIN textbox</param>
        /// <param name="cbBrand">Brand combobox</param>
        /// <param name="cbLocation">Location combobox</param>
        /// <param name="txtSalesman">Salesman textbox</param>
        /// <param name="dpDocDate">Document date picker</param>
        /// <param name="txtDocRemarks">Document remarks textbox</param>
        /// <param name="gridDataTable">Grid data table</param>
        public static void ClearForm(TextEdit txtEstimate, TextEdit txtCustomer, TextEdit txtVehicle,
            TextEdit txtVIN, ComboBoxEdit cbBrand, ComboBoxEdit cbLocation, TextEdit txtSalesman,
            DateEdit dpDocDate, MemoEdit txtDocRemarks, DataTable gridDataTable)
        {
            try
            {
                Debug.WriteLine("=== ClearForm: Clearing all form controls ===");

                txtEstimate.Text = "";
                txtCustomer.Text = "";
                txtVehicle.Text = "";
                txtVIN.Text = "";
                cbBrand.Text = "";
                cbLocation.Text = "";
                txtSalesman.Text = "";
                dpDocDate.DateTime = DateTime.Now;
                txtDocRemarks.Text = "";

                // Clear grid
                gridDataTable?.Clear();

                Debug.WriteLine("=== ClearForm: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in ClearForm: {ex.Message}");
                // Don't throw - this is not critical
            }
        }

        /// <summary>
        /// Enables or disables form controls for editing
        /// </summary>
        /// <param name="enabled">True to enable controls, false to disable</param>
        /// <param name="controls">Array of controls to enable/disable</param>
        public static void EnableControls(bool enabled, params Control[] controls)
        {
            try
            {
                Debug.WriteLine($"=== EnableControls: Setting enabled = {enabled} ===");

                foreach (var control in controls)
                {
                    if (control != null)
                    {
                        control.Enabled = enabled;
                    }
                }

                Debug.WriteLine($"=== EnableControls: Completed for {controls.Length} controls ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in EnableControls: {ex.Message}");
                // Don't throw - this is not critical
            }
        }

        /// <summary>
        /// Clears all form controls
        /// </summary>
        /// <param name="form">The form to clear</param>
        public static void ClearForm(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== ClearForm: Starting ===");

                // Clear header controls
                form.txtEstimate.Text = "";
                form.txtCustomer.Text = "";
                form.txtVehicle.Text = "";
                form.txtVIN.Text = "";
                form.cbBrand.Text = "";
                form.cbLocation.Text = "";
                form.txtSalesman.Text = "";
                form.dpDocDate.DateTime = DateTime.Now;
                form.txtDocRemarks.Text = "";

                // Clear grid
                if (form.GridDataTable != null)
                {
                    form.GridDataTable.Clear();
                }

                Debug.WriteLine("=== ClearForm: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ClearForm: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates button states based on edit mode and current estimate
        /// </summary>
        /// <param name="form">The form instance</param>
        /// <param name="isEditMode">Whether form is in edit mode</param>
        public static void UpdateButtonStates(dynamic form, bool isEditMode)
        {
            try
            {
                Debug.WriteLine($"=== UpdateButtonStates: isEditMode={isEditMode} ===");

                bool hasCurrentEstimate = form.CurrentEstimate != null;
                bool isEstimateClosed = hasCurrentEstimate && form.CurrentEstimate.Status; // true = closed

                // Check if there are any estimates in the database for navigation
                bool hasEstimatesInDatabase = false;
                try
                {
                    hasEstimatesInDatabase = EstimateFormNavigation.GetEstimateCount() > 0;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error checking estimate count: {ex.Message}");
                    hasEstimatesInDatabase = false;
                }

                // Navigation buttons - enabled when not in edit mode and there are estimates in database
                form.BarButtonItemFirst.Enabled = !isEditMode && hasEstimatesInDatabase;
                form.BarButtonItemPrevious.Enabled = !isEditMode && hasEstimatesInDatabase;
                form.BarButtonItemNext.Enabled = !isEditMode && hasEstimatesInDatabase;
                form.BarButtonItemLast.Enabled = !isEditMode && hasEstimatesInDatabase;

                // CRUD buttons - respect estimate closed status
                form.BarButtonItemNew.Enabled = !isEditMode;
                form.BarButtonItemEdit.Enabled = !isEditMode && hasCurrentEstimate && !isEstimateClosed; // Disable if closed
                form.BarButtonItemSave.Enabled = isEditMode && !isEstimateClosed; // Disable if closed
                form.BarButtonItemCancel.Enabled = isEditMode;
                form.BarButtonItemDelete.Enabled = !isEditMode && hasCurrentEstimate && !isEstimateClosed; // Disable if closed

                // Grid buttons - respect estimate closed status
                form.BarButtonItemAddRow.Enabled = isEditMode && !isEstimateClosed; // Disable if closed

                Debug.WriteLine($"=== UpdateButtonStates: hasCurrentEstimate={hasCurrentEstimate}, hasEstimatesInDatabase={hasEstimatesInDatabase}, isEstimateClosed={isEstimateClosed} ===");
                Debug.WriteLine("=== UpdateButtonStates: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in UpdateButtonStates: {ex.Message}");
            }
        }

        /// <summary>
        /// Enables or disables form controls for editing
        /// </summary>
        /// <param name="form">The form instance</param>
        /// <param name="enabled">True to enable controls, false to disable</param>
        public static void EnableControls(dynamic form, bool enabled)
        {
            try
            {
                Debug.WriteLine($"=== EnableControls: Setting enabled = {enabled} ===");

                // Enable/disable header controls
                form.txtEstimate.Enabled = enabled;
                form.txtCustomer.Enabled = enabled;
                form.txtVehicle.Enabled = enabled;
                form.txtVIN.Enabled = enabled;
                form.cbBrand.Enabled = enabled;
                form.cbLocation.Enabled = enabled;
                form.txtSalesman.Enabled = enabled;
                form.dpDocDate.Enabled = enabled;
                form.txtDocRemarks.Enabled = enabled;

                // Set grid edit mode (grid stays enabled for navigation but becomes read-only when not editing)
                EstimateFormGridManager.SetGridEditMode(form, enabled);

                Debug.WriteLine("=== EnableControls: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EnableControls: {ex.Message}");
            }
        }

        #endregion
    }
}
