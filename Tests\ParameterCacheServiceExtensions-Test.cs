// ParameterCacheServiceExtensions Test - Verification of typed extension methods
// Usage: Test file to verify the new typed parameter access methods work correctly

using System;
using System.Diagnostics;
using ProManage.Modules.Services;
using ProManage.Modules.Data.ParametersForm;

namespace ProManage.Tests
{
    /// <summary>
    /// Test class for verifying ParameterCacheServiceExtensions functionality
    /// This class provides methods to test the new typed parameter access methods
    /// </summary>
    public static class ParameterCacheServiceExtensionsTest
    {
        /// <summary>
        /// Runs comprehensive tests for all typed parameter access methods
        /// </summary>
        public static void RunAllTests()
        {
            Debug.WriteLine("=== ParameterCacheServiceExtensions Test Suite Starting ===");
            
            try
            {
                // Ensure parameter cache is initialized
                if (!ParameterCacheService.Instance.IsLoaded)
                {
                    Debug.WriteLine("Parameter cache not loaded, attempting to initialize...");
                    bool initialized = ParameterCacheService.Instance.Initialize();
                    Debug.WriteLine($"Parameter cache initialization result: {initialized}");
                }

                // Test integer parameter access
                TestIntegerParameters();

                // Test boolean parameter access
                TestBooleanParameters();

                // Test decimal parameter access
                TestDecimalParameters();

                // Test double parameter access
                TestDoubleParameters();

                // Test DateTime parameter access
                TestDateTimeParameters();

                // Test enum parameter access
                TestEnumParameters();

                // Test utility methods
                TestUtilityMethods();

                Debug.WriteLine("=== ParameterCacheServiceExtensions Test Suite Completed Successfully ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"=== ParameterCacheServiceExtensions Test Suite Failed: {ex.Message} ===");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Tests integer parameter access methods
        /// </summary>
        private static void TestIntegerParameters()
        {
            Debug.WriteLine("--- Testing Integer Parameter Access ---");

            try
            {
                // Test with existing parameter (if any)
                int decimals = ParameterCacheService.Instance.GetInt("DECIMALS", 2);
                Debug.WriteLine($"DECIMALS parameter: {decimals} (default: 2)");

                // Test with non-existent parameter
                int nonExistent = ParameterCacheService.Instance.GetInt("NON_EXISTENT_INT", 42);
                Debug.WriteLine($"NON_EXISTENT_INT parameter: {nonExistent} (should be default: 42)");

                // Test with null parameter code
                int nullTest = ParameterCacheService.Instance.GetInt(null, 99);
                Debug.WriteLine($"Null parameter code test: {nullTest} (should be default: 99)");

                Debug.WriteLine("Integer parameter tests completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in integer parameter tests: {ex.Message}");
            }
        }

        /// <summary>
        /// Tests boolean parameter access methods
        /// </summary>
        private static void TestBooleanParameters()
        {
            Debug.WriteLine("--- Testing Boolean Parameter Access ---");

            try
            {
                // Test with various boolean representations
                bool enableLogging = ParameterCacheService.Instance.GetBool("ENABLE_LOGGING", true);
                Debug.WriteLine($"ENABLE_LOGGING parameter: {enableLogging} (default: true)");

                // Test with non-existent parameter
                bool nonExistent = ParameterCacheService.Instance.GetBool("NON_EXISTENT_BOOL", false);
                Debug.WriteLine($"NON_EXISTENT_BOOL parameter: {nonExistent} (should be default: false)");

                Debug.WriteLine("Boolean parameter tests completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in boolean parameter tests: {ex.Message}");
            }
        }

        /// <summary>
        /// Tests decimal parameter access methods
        /// </summary>
        private static void TestDecimalParameters()
        {
            Debug.WriteLine("--- Testing Decimal Parameter Access ---");

            try
            {
                // Test with existing parameter (if any)
                decimal taxRate = ParameterCacheService.Instance.GetDecimal("TAX_RATE", 5.0m);
                Debug.WriteLine($"TAX_RATE parameter: {taxRate} (default: 5.0)");

                // Test with non-existent parameter
                decimal nonExistent = ParameterCacheService.Instance.GetDecimal("NON_EXISTENT_DECIMAL", 10.5m);
                Debug.WriteLine($"NON_EXISTENT_DECIMAL parameter: {nonExistent} (should be default: 10.5)");

                Debug.WriteLine("Decimal parameter tests completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in decimal parameter tests: {ex.Message}");
            }
        }

        /// <summary>
        /// Tests double parameter access methods
        /// </summary>
        private static void TestDoubleParameters()
        {
            Debug.WriteLine("--- Testing Double Parameter Access ---");

            try
            {
                // Test with existing parameter (if any)
                double multiplier = ParameterCacheService.Instance.GetDouble("MULTIPLIER", 1.0);
                Debug.WriteLine($"MULTIPLIER parameter: {multiplier} (default: 1.0)");

                // Test with non-existent parameter
                double nonExistent = ParameterCacheService.Instance.GetDouble("NON_EXISTENT_DOUBLE", 3.14159);
                Debug.WriteLine($"NON_EXISTENT_DOUBLE parameter: {nonExistent} (should be default: 3.14159)");

                Debug.WriteLine("Double parameter tests completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in double parameter tests: {ex.Message}");
            }
        }

        /// <summary>
        /// Tests DateTime parameter access methods
        /// </summary>
        private static void TestDateTimeParameters()
        {
            Debug.WriteLine("--- Testing DateTime Parameter Access ---");

            try
            {
                // Test with existing parameter (if any)
                DateTime cutoffDate = ParameterCacheService.Instance.GetDateTime("CUTOFF_DATE", DateTime.Today);
                Debug.WriteLine($"CUTOFF_DATE parameter: {cutoffDate} (default: {DateTime.Today})");

                // Test with non-existent parameter
                DateTime defaultDate = new DateTime(2024, 1, 1);
                DateTime nonExistent = ParameterCacheService.Instance.GetDateTime("NON_EXISTENT_DATE", defaultDate);
                Debug.WriteLine($"NON_EXISTENT_DATE parameter: {nonExistent} (should be default: {defaultDate})");

                Debug.WriteLine("DateTime parameter tests completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DateTime parameter tests: {ex.Message}");
            }
        }

        /// <summary>
        /// Tests enum parameter access methods
        /// </summary>
        private static void TestEnumParameters()
        {
            Debug.WriteLine("--- Testing Enum Parameter Access ---");

            try
            {
                // Test with DayOfWeek enum as an example
                DayOfWeek startDay = ParameterCacheService.Instance.GetEnum("START_DAY", DayOfWeek.Monday);
                Debug.WriteLine($"START_DAY parameter: {startDay} (default: Monday)");

                // Test with non-existent parameter
                DayOfWeek nonExistent = ParameterCacheService.Instance.GetEnum("NON_EXISTENT_ENUM", DayOfWeek.Sunday);
                Debug.WriteLine($"NON_EXISTENT_ENUM parameter: {nonExistent} (should be default: Sunday)");

                Debug.WriteLine("Enum parameter tests completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in enum parameter tests: {ex.Message}");
            }
        }

        /// <summary>
        /// Tests utility methods
        /// </summary>
        private static void TestUtilityMethods()
        {
            Debug.WriteLine("--- Testing Utility Methods ---");

            try
            {
                // Test enhanced logging method
                string currency = ParameterCacheService.Instance.GetParameterWithLogging("CURRENCY", "USD");
                Debug.WriteLine($"CURRENCY parameter with logging: {currency}");

                // Test with non-existent parameter
                string nonExistent = ParameterCacheService.Instance.GetParameterWithLogging("NON_EXISTENT_PARAM", "DEFAULT");
                Debug.WriteLine($"NON_EXISTENT_PARAM with logging: {nonExistent}");

                Debug.WriteLine("Utility method tests completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in utility method tests: {ex.Message}");
            }
        }

        /// <summary>
        /// Tests backward compatibility with existing methods
        /// </summary>
        public static void TestBackwardCompatibility()
        {
            Debug.WriteLine("--- Testing Backward Compatibility ---");

            try
            {
                // Ensure existing GetParameter methods still work
                string currency1 = ParameterCacheService.Instance.GetParameter("CURRENCY");
                string currency2 = ParameterCacheService.Instance.GetParameter("CURRENCY", "USD");
                
                Debug.WriteLine($"Existing GetParameter() method: '{currency1}'");
                Debug.WriteLine($"Existing GetParameter(default) method: '{currency2}'");

                // Test that service properties still work
                Debug.WriteLine($"Service IsLoaded: {ParameterCacheService.Instance.IsLoaded}");
                Debug.WriteLine($"Service ParameterCount: {ParameterCacheService.Instance.ParameterCount}");
                Debug.WriteLine($"Service LastCacheUpdate: {ParameterCacheService.Instance.LastCacheUpdate}");

                Debug.WriteLine("Backward compatibility tests completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in backward compatibility tests: {ex.Message}");
            }
        }
    }
}
