// ParameterCacheServiceExtensions - Typed extension methods for ParameterCacheService
// Usage: Provides type-safe parameter access with automatic conversion and validation

using System;
using System.Diagnostics;
using System.Globalization;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Extension methods for ParameterCacheService providing typed parameter access
    /// These methods provide type-safe parameter retrieval with automatic conversion and validation
    /// </summary>
    public static class ParameterCacheServiceExtensions
    {
        #region Integer Parameter Access

        /// <summary>
        /// Gets a parameter value as an integer with automatic type conversion
        /// </summary>
        /// <param name="service">The ParameterCacheService instance</param>
        /// <param name="parameterCode">The parameter code to retrieve</param>
        /// <param name="defaultValue">Default value to return if parameter not found or conversion fails</param>
        /// <returns>Parameter value as integer, or default value if not found/invalid</returns>
        /// <example>
        /// int decimals = ParameterCacheService.Instance.GetInt("DECIMALS", 2);
        /// </example>
        public static int GetInt(this ParameterCacheService service, string parameterCode, int defaultValue = 0)
        {
            try
            {
                if (service == null)
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetInt: Service is null, returning default value {defaultValue}");
                    return defaultValue;
                }

                if (string.IsNullOrWhiteSpace(parameterCode))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetInt: Parameter code is null/empty, returning default value {defaultValue}");
                    return defaultValue;
                }

                // Get the raw parameter value
                string rawValue = service.GetParameter(parameterCode);
                
                if (string.IsNullOrWhiteSpace(rawValue))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetInt: Parameter '{parameterCode}' not found, returning default value {defaultValue}");
                    return defaultValue;
                }

                // Attempt to parse the value as integer
                if (int.TryParse(rawValue.Trim(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int result))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetInt: Parameter '{parameterCode}' = {result}");
                    return result;
                }

                Debug.WriteLine($"ParameterCacheServiceExtensions.GetInt: Failed to parse parameter '{parameterCode}' value '{rawValue}' as integer, returning default value {defaultValue}");
                return defaultValue;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ParameterCacheServiceExtensions.GetInt: Error getting parameter '{parameterCode}': {ex.Message}");
                return defaultValue;
            }
        }

        #endregion

        #region Boolean Parameter Access

        /// <summary>
        /// Gets a parameter value as a boolean with automatic type conversion
        /// Supports various boolean representations: true/false, 1/0, yes/no, on/off
        /// </summary>
        /// <param name="service">The ParameterCacheService instance</param>
        /// <param name="parameterCode">The parameter code to retrieve</param>
        /// <param name="defaultValue">Default value to return if parameter not found or conversion fails</param>
        /// <returns>Parameter value as boolean, or default value if not found/invalid</returns>
        /// <example>
        /// bool enableFeature = ParameterCacheService.Instance.GetBool("ENABLE_FEATURE_X", false);
        /// </example>
        public static bool GetBool(this ParameterCacheService service, string parameterCode, bool defaultValue = false)
        {
            try
            {
                if (service == null)
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetBool: Service is null, returning default value {defaultValue}");
                    return defaultValue;
                }

                if (string.IsNullOrWhiteSpace(parameterCode))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetBool: Parameter code is null/empty, returning default value {defaultValue}");
                    return defaultValue;
                }

                // Get the raw parameter value
                string rawValue = service.GetParameter(parameterCode);
                
                if (string.IsNullOrWhiteSpace(rawValue))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetBool: Parameter '{parameterCode}' not found, returning default value {defaultValue}");
                    return defaultValue;
                }

                // Normalize the value for comparison
                string normalizedValue = rawValue.Trim().ToLowerInvariant();

                // Check for various boolean representations
                bool result;
                switch (normalizedValue)
                {
                    case "true":
                    case "1":
                    case "yes":
                    case "on":
                    case "enabled":
                    case "y":
                        result = true;
                        break;
                    case "false":
                    case "0":
                    case "no":
                    case "off":
                    case "disabled":
                    case "n":
                        result = false;
                        break;
                    default:
                        result = defaultValue;
                        break;
                }

                // If we used the default value due to unrecognized format, log it
                if (result == defaultValue && !IsBooleanValue(normalizedValue))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetBool: Failed to parse parameter '{parameterCode}' value '{rawValue}' as boolean, returning default value {defaultValue}");
                }
                else
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetBool: Parameter '{parameterCode}' = {result}");
                }

                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ParameterCacheServiceExtensions.GetBool: Error getting parameter '{parameterCode}': {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// Helper method to check if a value represents a boolean
        /// </summary>
        private static bool IsBooleanValue(string normalizedValue)
        {
            switch (normalizedValue)
            {
                case "true":
                case "false":
                case "1":
                case "0":
                case "yes":
                case "no":
                case "on":
                case "off":
                case "enabled":
                case "disabled":
                case "y":
                case "n":
                    return true;
                default:
                    return false;
            }
        }

        #endregion

        #region Decimal Parameter Access

        /// <summary>
        /// Gets a parameter value as a decimal with automatic type conversion
        /// </summary>
        /// <param name="service">The ParameterCacheService instance</param>
        /// <param name="parameterCode">The parameter code to retrieve</param>
        /// <param name="defaultValue">Default value to return if parameter not found or conversion fails</param>
        /// <returns>Parameter value as decimal, or default value if not found/invalid</returns>
        /// <example>
        /// decimal taxRate = ParameterCacheService.Instance.GetDecimal("TAX_RATE", 5.0m);
        /// </example>
        public static decimal GetDecimal(this ParameterCacheService service, string parameterCode, decimal defaultValue = 0m)
        {
            try
            {
                if (service == null)
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetDecimal: Service is null, returning default value {defaultValue}");
                    return defaultValue;
                }

                if (string.IsNullOrWhiteSpace(parameterCode))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetDecimal: Parameter code is null/empty, returning default value {defaultValue}");
                    return defaultValue;
                }

                // Get the raw parameter value
                string rawValue = service.GetParameter(parameterCode);
                
                if (string.IsNullOrWhiteSpace(rawValue))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetDecimal: Parameter '{parameterCode}' not found, returning default value {defaultValue}");
                    return defaultValue;
                }

                // Attempt to parse the value as decimal
                if (decimal.TryParse(rawValue.Trim(), NumberStyles.Number, CultureInfo.InvariantCulture, out decimal result))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetDecimal: Parameter '{parameterCode}' = {result}");
                    return result;
                }

                Debug.WriteLine($"ParameterCacheServiceExtensions.GetDecimal: Failed to parse parameter '{parameterCode}' value '{rawValue}' as decimal, returning default value {defaultValue}");
                return defaultValue;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ParameterCacheServiceExtensions.GetDecimal: Error getting parameter '{parameterCode}': {ex.Message}");
                return defaultValue;
            }
        }

        #endregion

        #region Double Parameter Access

        /// <summary>
        /// Gets a parameter value as a double with automatic type conversion
        /// </summary>
        /// <param name="service">The ParameterCacheService instance</param>
        /// <param name="parameterCode">The parameter code to retrieve</param>
        /// <param name="defaultValue">Default value to return if parameter not found or conversion fails</param>
        /// <returns>Parameter value as double, or default value if not found/invalid</returns>
        /// <example>
        /// double multiplier = ParameterCacheService.Instance.GetDouble("MULTIPLIER", 1.0);
        /// </example>
        public static double GetDouble(this ParameterCacheService service, string parameterCode, double defaultValue = 0.0)
        {
            try
            {
                if (service == null)
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetDouble: Service is null, returning default value {defaultValue}");
                    return defaultValue;
                }

                if (string.IsNullOrWhiteSpace(parameterCode))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetDouble: Parameter code is null/empty, returning default value {defaultValue}");
                    return defaultValue;
                }

                // Get the raw parameter value
                string rawValue = service.GetParameter(parameterCode);
                
                if (string.IsNullOrWhiteSpace(rawValue))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetDouble: Parameter '{parameterCode}' not found, returning default value {defaultValue}");
                    return defaultValue;
                }

                // Attempt to parse the value as double
                if (double.TryParse(rawValue.Trim(), NumberStyles.Float | NumberStyles.AllowThousands, CultureInfo.InvariantCulture, out double result))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetDouble: Parameter '{parameterCode}' = {result}");
                    return result;
                }

                Debug.WriteLine($"ParameterCacheServiceExtensions.GetDouble: Failed to parse parameter '{parameterCode}' value '{rawValue}' as double, returning default value {defaultValue}");
                return defaultValue;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ParameterCacheServiceExtensions.GetDouble: Error getting parameter '{parameterCode}': {ex.Message}");
                return defaultValue;
            }
        }

        #endregion

        #region DateTime Parameter Access

        /// <summary>
        /// Gets a parameter value as a DateTime with automatic type conversion
        /// Supports various date formats and uses InvariantCulture for parsing
        /// </summary>
        /// <param name="service">The ParameterCacheService instance</param>
        /// <param name="parameterCode">The parameter code to retrieve</param>
        /// <param name="defaultValue">Default value to return if parameter not found or conversion fails. If null, uses DateTime.Now</param>
        /// <returns>Parameter value as DateTime, or default value if not found/invalid</returns>
        /// <example>
        /// DateTime cutoffDate = ParameterCacheService.Instance.GetDateTime("CUTOFF_DATE", DateTime.Today);
        /// </example>
        public static DateTime GetDateTime(this ParameterCacheService service, string parameterCode, DateTime? defaultValue = null)
        {
            try
            {
                DateTime actualDefault = defaultValue ?? DateTime.Now;

                if (service == null)
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetDateTime: Service is null, returning default value {actualDefault}");
                    return actualDefault;
                }

                if (string.IsNullOrWhiteSpace(parameterCode))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetDateTime: Parameter code is null/empty, returning default value {actualDefault}");
                    return actualDefault;
                }

                // Get the raw parameter value
                string rawValue = service.GetParameter(parameterCode);

                if (string.IsNullOrWhiteSpace(rawValue))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetDateTime: Parameter '{parameterCode}' not found, returning default value {actualDefault}");
                    return actualDefault;
                }

                // Attempt to parse the value as DateTime using various formats
                if (DateTime.TryParse(rawValue.Trim(), CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime result))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetDateTime: Parameter '{parameterCode}' = {result}");
                    return result;
                }

                // Try ISO 8601 format specifically
                if (DateTime.TryParseExact(rawValue.Trim(), "yyyy-MM-ddTHH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out result))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetDateTime: Parameter '{parameterCode}' = {result} (ISO format)");
                    return result;
                }

                // Try date-only format
                if (DateTime.TryParseExact(rawValue.Trim(), "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out result))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetDateTime: Parameter '{parameterCode}' = {result} (date-only format)");
                    return result;
                }

                Debug.WriteLine($"ParameterCacheServiceExtensions.GetDateTime: Failed to parse parameter '{parameterCode}' value '{rawValue}' as DateTime, returning default value {actualDefault}");
                return actualDefault;
            }
            catch (Exception ex)
            {
                DateTime actualDefault = defaultValue ?? DateTime.Now;
                Debug.WriteLine($"ParameterCacheServiceExtensions.GetDateTime: Error getting parameter '{parameterCode}': {ex.Message}");
                return actualDefault;
            }
        }

        #endregion

        #region Enum Parameter Access

        /// <summary>
        /// Gets a parameter value as an enum with automatic type conversion
        /// Supports both string names and numeric values for enum conversion
        /// </summary>
        /// <typeparam name="TEnum">The enum type to convert to</typeparam>
        /// <param name="service">The ParameterCacheService instance</param>
        /// <param name="parameterCode">The parameter code to retrieve</param>
        /// <param name="defaultValue">Default value to return if parameter not found or conversion fails</param>
        /// <param name="ignoreCase">Whether to ignore case when parsing enum names (default: true)</param>
        /// <returns>Parameter value as enum, or default value if not found/invalid</returns>
        /// <example>
        /// LogLevel level = ParameterCacheService.Instance.GetEnum("LOG_LEVEL", LogLevel.Info);
        /// </example>
        public static TEnum GetEnum<TEnum>(this ParameterCacheService service, string parameterCode, TEnum defaultValue = default, bool ignoreCase = true)
            where TEnum : struct, Enum
        {
            try
            {
                if (service == null)
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetEnum<{typeof(TEnum).Name}>: Service is null, returning default value {defaultValue}");
                    return defaultValue;
                }

                if (string.IsNullOrWhiteSpace(parameterCode))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetEnum<{typeof(TEnum).Name}>: Parameter code is null/empty, returning default value {defaultValue}");
                    return defaultValue;
                }

                // Get the raw parameter value
                string rawValue = service.GetParameter(parameterCode);

                if (string.IsNullOrWhiteSpace(rawValue))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetEnum<{typeof(TEnum).Name}>: Parameter '{parameterCode}' not found, returning default value {defaultValue}");
                    return defaultValue;
                }

                // Attempt to parse the value as enum
                if (Enum.TryParse<TEnum>(rawValue.Trim(), ignoreCase, out TEnum result))
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetEnum<{typeof(TEnum).Name}>: Parameter '{parameterCode}' = {result}");
                    return result;
                }

                Debug.WriteLine($"ParameterCacheServiceExtensions.GetEnum<{typeof(TEnum).Name}>: Failed to parse parameter '{parameterCode}' value '{rawValue}' as {typeof(TEnum).Name}, returning default value {defaultValue}");
                return defaultValue;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ParameterCacheServiceExtensions.GetEnum<{typeof(TEnum).Name}>: Error getting parameter '{parameterCode}': {ex.Message}");
                return defaultValue;
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Gets a parameter value with enhanced logging for debugging purposes
        /// This method provides detailed logging of parameter access for troubleshooting
        /// </summary>
        /// <param name="service">The ParameterCacheService instance</param>
        /// <param name="parameterCode">The parameter code to retrieve</param>
        /// <param name="defaultValue">Default value to return if parameter not found</param>
        /// <returns>Parameter value or default value</returns>
        public static string GetParameterWithLogging(this ParameterCacheService service, string parameterCode, string defaultValue = "")
        {
            try
            {
                Debug.WriteLine($"ParameterCacheServiceExtensions.GetParameterWithLogging: Requesting parameter '{parameterCode}'");

                if (service == null)
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetParameterWithLogging: Service is null");
                    return defaultValue;
                }

                if (!service.IsLoaded)
                {
                    Debug.WriteLine($"ParameterCacheServiceExtensions.GetParameterWithLogging: Service not loaded, parameter count: {service.ParameterCount}");
                }

                string result = service.GetParameter(parameterCode, defaultValue);
                Debug.WriteLine($"ParameterCacheServiceExtensions.GetParameterWithLogging: Parameter '{parameterCode}' = '{result}' (using default: {result == defaultValue})");

                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ParameterCacheServiceExtensions.GetParameterWithLogging: Error getting parameter '{parameterCode}': {ex.Message}");
                return defaultValue;
            }
        }

        #endregion
    }
}
