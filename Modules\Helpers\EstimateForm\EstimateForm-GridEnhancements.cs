using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Windows.Forms;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.Data;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraGrid.Views.Base;
using System.Diagnostics;
using ProManage.Modules.Services;
using ProManage.Modules.Helpers;

namespace ProManage.Modules.Helpers.EstimateForm
{
    /// <summary>
    /// Grid enhancements for EstimateForm including formatting, validation, and totals
    /// Handles real-time uppercase conversion, numeric validation, currency formatting, and summary rows
    /// </summary>
    public static class EstimateFormGridEnhancements
    {
        #region Public Methods

        /// <summary>
        /// Sets up all grid enhancements including events, formatting, and totals
        /// </summary>
        /// <param name="gridView">The grid view to enhance</param>
        public static void SetupGridEnhancements(GridView gridView)
        {
            try
            {
                Debug.WriteLine("=== EstimateFormGridEnhancements.SetupGridEnhancements: Starting ===");

                // Wire up grid events
                SetupGridEvents(gridView);

                // Setup currency formatting
                SetupCurrencyFormatting(gridView);

                // Setup summary/total rows
                SetupSummaryRows(gridView);

                Debug.WriteLine("=== EstimateFormGridEnhancements.SetupGridEnhancements: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in SetupGridEnhancements: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Sets up grid events for validation and formatting
        /// </summary>
        private static void SetupGridEvents(GridView gridView)
        {
            try
            {
                // Remove existing event handlers to avoid duplicates
                gridView.CellValueChanging -= GridView_CellValueChanging;
                gridView.ValidatingEditor -= GridView_ValidatingEditor;
                gridView.ValidateRow -= GridView_ValidateRow;
                gridView.ShownEditor -= GridView_ShownEditor;
                gridView.CellValueChanged -= GridView_CellValueChanged;

                // Add event handlers
                gridView.CellValueChanging += GridView_CellValueChanging;
                gridView.ValidatingEditor += GridView_ValidatingEditor;
                gridView.ValidateRow += GridView_ValidateRow;
                gridView.ShownEditor += GridView_ShownEditor;
                gridView.CellValueChanged += GridView_CellValueChanged;

                Debug.WriteLine("Grid events setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up grid events: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Sets up delete button event handler for the grid
        /// </summary>
        /// <param name="gridView">The grid view to setup</param>
        /// <param name="gridDataTable">The data table bound to the grid</param>
        public static void SetupDeleteButtonEvents(GridView gridView, DataTable gridDataTable)
        {
            try
            {
                Debug.WriteLine("=== SetupDeleteButtonEvents: Starting ===");

                // Find the delete column and setup its repository item
                var deleteColumn = gridView.Columns["Delete"];
                if (deleteColumn != null && deleteColumn.ColumnEdit is DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit buttonEdit)
                {
                    // Remove existing event handler to avoid duplicates
                    buttonEdit.ButtonClick -= (sender, e) => DeleteButton_Click(sender, e, gridView, gridDataTable);

                    // Add event handler for delete button
                    buttonEdit.ButtonClick += (sender, e) => DeleteButton_Click(sender, e, gridView, gridDataTable);
                }

                Debug.WriteLine("=== SetupDeleteButtonEvents: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in SetupDeleteButtonEvents: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Sets up currency formatting for price columns using centralized currency formatting service
        /// </summary>
        private static void SetupCurrencyFormatting(GridView gridView)
        {
            try
            {
                Debug.WriteLine("=== SetupCurrencyFormatting: Using CurrencyFormattingService ===");

                // Use centralized currency formatting service
                CurrencyFormattingService.ApplyCurrencyFormatting(gridView, "OEPrice", "AFMPrice");

                Debug.WriteLine("Currency formatting setup completed using CurrencyFormattingService");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up currency formatting: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Sets up footer totals for numeric columns using proper DevExpress approach
        /// </summary>
        private static void SetupSummaryRows(GridView gridView)
        {
            try
            {
                Debug.WriteLine("=== SetupSummaryRows: Starting ===");

                // Enable footer to show totals
                gridView.OptionsView.ShowFooter = true;

                // Clear existing summary items from all columns
                foreach (DevExpress.XtraGrid.Columns.GridColumn column in gridView.Columns)
                {
                    column.Summary.Clear();
                }

                // Add count summary for Serial Number column (S.No)
                var serialColumn = gridView.Columns["SerialNumber"];
                if (serialColumn != null)
                {
                    var countSummary = new DevExpress.XtraGrid.GridColumnSummaryItem(SummaryItemType.Count, "SerialNumber", "Count: {0}");
                    serialColumn.Summary.Add(countSummary);
                    Debug.WriteLine("✓ Serial Number count summary added");
                }

                // Add summary for Quantity column
                var qtyColumn = gridView.Columns["Quantity"];
                if (qtyColumn != null)
                {
                    var qtySummary = new DevExpress.XtraGrid.GridColumnSummaryItem(SummaryItemType.Sum, "Quantity", "Total: {0:N0}");
                    qtyColumn.Summary.Add(qtySummary);
                    Debug.WriteLine("✓ Quantity column summary added");
                }

                // Use centralized currency formatting service for summaries
                CurrencyFormattingService.ApplyCurrencySummaries(gridView, new[] { "OEPrice", "AFMPrice" });
                Debug.WriteLine("✓ Currency summaries applied using CurrencyFormattingService");

                Debug.WriteLine("=== SetupSummaryRows: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up summary rows: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles cell value changing - NO uppercase conversion here to avoid cursor issues
        /// </summary>
        private static void GridView_CellValueChanging(object sender, CellValueChangedEventArgs e)
        {
            try
            {
                var gridView = sender as GridView;
                if (gridView == null) return;

                // NO uppercase conversion here - moved to CellValueChanged to avoid cursor issues
                Debug.WriteLine($"Cell value changing: Column={e.Column?.FieldName}, Value={e.Value}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView_CellValueChanging: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles editor validation for numeric fields
        /// </summary>
        private static void GridView_ValidatingEditor(object sender, BaseContainerValidateEditorEventArgs e)
        {
            try
            {
                var gridView = sender as GridView;
                if (gridView == null) return;

                var column = gridView.FocusedColumn;
                if (column == null) return;

                // Validate numeric fields
                if (column.FieldName == "Quantity" || column.FieldName == "OEPrice" || column.FieldName == "AFMPrice")
                {
                    if (e.Value != null && !string.IsNullOrWhiteSpace(e.Value.ToString()))
                    {
                        // Try to parse as decimal for price fields
                        if (column.FieldName == "OEPrice" || column.FieldName == "AFMPrice")
                        {
                            if (!decimal.TryParse(e.Value.ToString(), out decimal decimalValue) || decimalValue < 0)
                            {
                                e.Valid = false;
                                e.ErrorText = "Please enter a valid positive number for price.";
                            }
                        }
                        // Try to parse as integer for quantity
                        else if (column.FieldName == "Quantity")
                        {
                            if (!int.TryParse(e.Value.ToString(), out int intValue) || intValue < 0)
                            {
                                e.Valid = false;
                                e.ErrorText = "Please enter a valid positive integer for quantity.";
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView_ValidatingEditor: {ex.Message}");
            }
        }



        /// <summary>
        /// Handles row validation for mandatory fields
        /// </summary>
        private static void GridView_ValidateRow(object sender, ValidateRowEventArgs e)
        {
            try
            {
                var gridView = sender as GridView;
                if (gridView == null) return;

                // Get the data row
                var dataRow = gridView.GetDataRow(e.RowHandle);
                if (dataRow == null) return;

                // Check if this is an empty row (skip validation for completely empty rows)
                bool isEmptyRow = string.IsNullOrWhiteSpace(dataRow["PartNumber"]?.ToString()) &&
                                 string.IsNullOrWhiteSpace(dataRow["Description"]?.ToString()) &&
                                 (dataRow["Quantity"] == DBNull.Value || dataRow["Quantity"] == null ||
                                  EstimateFormValidation.TryParseDecimal(dataRow["Quantity"]) == 0) &&
                                 (dataRow["OEPrice"] == DBNull.Value || dataRow["OEPrice"] == null ||
                                  EstimateFormValidation.TryParseDecimal(dataRow["OEPrice"]) == 0) &&
                                 (dataRow["AFMPrice"] == DBNull.Value || dataRow["AFMPrice"] == null ||
                                  EstimateFormValidation.TryParseDecimal(dataRow["AFMPrice"]) == 0);

                if (isEmptyRow)
                {
                    return; // Skip validation for empty rows
                }

                // Validate mandatory fields for non-empty rows
                var errors = new List<string>();

                // Part Number is required
                if (string.IsNullOrWhiteSpace(dataRow["PartNumber"]?.ToString()))
                {
                    errors.Add("Part Number is required");
                }

                // Description is required
                if (string.IsNullOrWhiteSpace(dataRow["Description"]?.ToString()))
                {
                    errors.Add("Description is required");
                }

                // Quantity is required and must be greater than 0
                var quantity = EstimateFormValidation.TryParseDecimal(dataRow["Quantity"]);
                if (!quantity.HasValue || quantity.Value <= 0)
                {
                    errors.Add("Quantity is required and must be greater than 0");
                }

                // At least one price (OE or AFM) should be provided
                var oePrice = EstimateFormValidation.TryParseDecimal(dataRow["OEPrice"]);
                var afmPrice = EstimateFormValidation.TryParseDecimal(dataRow["AFMPrice"]);
                if ((!oePrice.HasValue || oePrice.Value <= 0) && (!afmPrice.HasValue || afmPrice.Value <= 0))
                {
                    errors.Add("At least one price (OE Price or AFM Price) must be provided");
                }

                if (errors.Count > 0)
                {
                    e.Valid = false;
                    e.ErrorText = string.Join("; ", errors);
                    Debug.WriteLine($"Row validation failed: {e.ErrorText}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView_ValidateRow: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles when an editor is shown - TESTING multiple approaches for uppercase conversion
        /// </summary>
        private static void GridView_ShownEditor(object sender, EventArgs e)
        {
            try
            {
                var gridView = sender as GridView;
                if (gridView == null) return;

                // Check if this is a text column that needs uppercase conversion
                string columnName = gridView.FocusedColumn?.FieldName;
                Debug.WriteLine($"=== GridView_ShownEditor: Column={columnName} ===");

                if (columnName == "PartNumber" || columnName == "Description")
                {
                    var textEdit = gridView.ActiveEditor as DevExpress.XtraEditors.TextEdit;
                    if (textEdit != null)
                    {
                        Debug.WriteLine($"TextEdit found for {columnName}, setting up uppercase conversion...");

                        // Method 1: Try CharacterCasing
                        try
                        {
                            textEdit.Properties.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
                            Debug.WriteLine($"CharacterCasing.Upper set for {columnName}");
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"CharacterCasing failed: {ex.Message}");
                        }

                        // Method 2: Add KeyPress event as backup
                        try
                        {
                            // Remove existing handler to avoid duplicates
                            textEdit.KeyPress -= TextEdit_KeyPress_Uppercase;
                            // Add new handler
                            textEdit.KeyPress += TextEdit_KeyPress_Uppercase;
                            Debug.WriteLine($"KeyPress event added for {columnName}");
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"KeyPress event failed: {ex.Message}");
                        }

                        // Convert any existing text to uppercase
                        if (!string.IsNullOrEmpty(textEdit.Text))
                        {
                            string currentText = textEdit.Text;
                            string upperText = currentText.ToUpper();
                            if (currentText != upperText)
                            {
                                int cursorPos = textEdit.SelectionStart;
                                textEdit.Text = upperText;
                                textEdit.SelectionStart = Math.Min(cursorPos, upperText.Length);
                                textEdit.SelectionLength = 0;
                                Debug.WriteLine($"Existing text converted: '{currentText}' -> '{upperText}'");
                            }
                        }
                    }
                    else
                    {
                        Debug.WriteLine($"Could not get TextEdit for column: {columnName}");
                    }
                }
                else
                {
                    Debug.WriteLine($"Editor shown for non-text column: {columnName}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView_ShownEditor: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles KeyPress for uppercase conversion - backup method
        /// </summary>
        private static void TextEdit_KeyPress_Uppercase(object sender, KeyPressEventArgs e)
        {
            try
            {
                if (char.IsLetter(e.KeyChar))
                {
                    char originalChar = e.KeyChar;
                    e.KeyChar = char.ToUpper(e.KeyChar);
                    Debug.WriteLine($"KeyPress conversion: '{originalChar}' -> '{e.KeyChar}'");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in TextEdit_KeyPress_Uppercase: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles cell value changed - DISABLED uppercase conversion to fix cursor jumping
        /// </summary>
        private static void GridView_CellValueChanged(object sender, CellValueChangedEventArgs e)
        {
            try
            {
                var gridView = sender as GridView;
                if (gridView == null) return;

                // DISABLED: All uppercase conversion removed to fix cursor jumping to position 1
                // The cursor was jumping to the beginning causing text to appear in reverse order
                // Example: typing "hello" was showing as "olleh"

                Debug.WriteLine($"Cell value changed: Column={e.Column?.FieldName}, Value={e.Value}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView_CellValueChanged: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles delete button click
        /// </summary>
        private static void DeleteButton_Click(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e, GridView gridView, DataTable gridDataTable)
        {
            try
            {
                Debug.WriteLine("=== DeleteButton_Click: Starting ===");

                if (gridView == null || gridDataTable == null)
                {
                    Debug.WriteLine("GridView or DataTable is null");
                    return;
                }

                int rowHandle = gridView.FocusedRowHandle;
                if (rowHandle < 0)
                {
                    Debug.WriteLine("Invalid row handle");
                    return;
                }

                // Get the data row index
                int dataRowIndex = gridView.GetDataSourceRowIndex(rowHandle);
                if (dataRowIndex >= 0 && dataRowIndex < gridDataTable.Rows.Count)
                {
                    Debug.WriteLine($"Deleting row at index {dataRowIndex}");

                    // Remove the row from data table
                    gridDataTable.Rows.RemoveAt(dataRowIndex);

                    // Update serial numbers for remaining rows
                    EstimateFormGridManager.UpdateSerialNumbers(gridDataTable);

                    // Refresh the grid display
                    gridView.RefreshData();

                    Debug.WriteLine($"Row deleted successfully. Remaining rows: {gridDataTable.Rows.Count}");
                }
                else
                {
                    Debug.WriteLine($"Invalid data row index: {dataRowIndex}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DeleteButton_Click: {ex.Message}");
                MessageBox.Show($"Error deleting row: {ex.Message}", "Delete Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
